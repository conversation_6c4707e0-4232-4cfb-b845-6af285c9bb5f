#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立测试脚本：尝试用 PowerPoint COM 将指定 PPT/PPTX 导出为图片，验证是否可被渲染

使用示例：
  python test_render_ppt_file.py "C:\\path\\to\\your.pptx" --first-n 3

说明：
- 先尝试直接打开源文件；
- 如果失败，会复制到临时目录（短路径、ASCII 文件名）后再试；
- 默认导出前 3 页到临时目录，脚本会打印导出成功的图片路径。
"""

import argparse
import os
import re
import shutil
import sys
import tempfile
import time
import traceback


def sanitize_filename(name: str) -> str:
    base = os.path.basename(name)
    # 去掉扩展名，再做 ASCII 化和长度限制
    stem, ext = os.path.splitext(base)
    stem_ascii = re.sub(r"[^A-Za-z0-9_.-]", "_", stem)[:80]
    ext_ascii = re.sub(r"[^A-Za-z0-9_.-]", "_", ext)[:10]
    if not ext_ascii:
        ext_ascii = ".pptx"
    return stem_ascii + ext_ascii


def copy_to_temp_short_path(src_path: str) -> str:
    temp_dir = os.path.join(tempfile.gettempdir(), "ppt_render_test")
    os.makedirs(temp_dir, exist_ok=True)
    dst_name = sanitize_filename(src_path)
    dst_path = os.path.join(temp_dir, dst_name)
    try:
        shutil.copy2(src_path, dst_path)
        return dst_path
    except Exception:
        # 退一步用 shutil.copy
        shutil.copy(src_path, dst_path)
        return dst_path


def open_presentation(powerpoint, path: str):
    """尽可能稳妥地打开演示文稿，返回 presentation 对象或抛异常。"""
    # 先尝试无窗口，失败后再显式窗口
    try:
        return powerpoint.Presentations.Open(FileName=os.path.abspath(path), ReadOnly=True, Untitled=False, WithWindow=False)
    except Exception:
        return powerpoint.Presentations.Open(FileName=os.path.abspath(path), ReadOnly=True, Untitled=False, WithWindow=True)


def export_first_n_slides(presentation, out_dir: str, first_n: int = 3, image_format: str = "PNG") -> list:
    os.makedirs(out_dir, exist_ok=True)
    exported = []
    total = int(getattr(presentation.Slides, "Count", 0))
    if total <= 0:
        return exported
    last = min(first_n, total)
    for i in range(1, last + 1):
        out_path = os.path.join(out_dir, f"slide_{i}.{image_format.lower()}")
        try:
            presentation.Slides(i).Export(out_path, image_format)
            # 稍等片刻让写入落盘
            time.sleep(0.05)
            if os.path.exists(out_path):
                exported.append(out_path)
        except Exception:
            print(f"导出第 {i} 页失败:\n{traceback.format_exc()}")
    return exported


def test_render_once(ppt_path: str, first_n: int) -> int:
    try:
        import pythoncom
        import win32com.client
    except Exception as e:
        print(f"缺少 pywin32 依赖，请先安装：pip install pywin32\n错误: {e}")
        return 2

    # 先干净化残留进程（尽量避免被占用）
    try:
        os.system('taskkill /F /IM POWERPNT.EXE 2>nul')
        time.sleep(0.5)
    except Exception:
        pass

    pythoncom.CoInitialize()
    app = None
    pres = None
    try:
        app = win32com.client.DispatchEx("PowerPoint.Application")
        app.DisplayAlerts = 0
        app.Visible = True

        # 第一轮：直接打开原路径
        try:
            pres = open_presentation(app, ppt_path)
        except Exception as e1:
            print("直接打开失败，尝试复制到临时短路径后重试...")
            # 第二轮：复制到临时短路径再开
            try:
                temp_path = copy_to_temp_short_path(ppt_path)
                pres = open_presentation(app, temp_path)
                print(f"已从临时路径打开: {temp_path}")
            except Exception as e2:
                print("两次打开均失败。详细错误如下：")
                print("- 直接打开错误:\n" + traceback.format_exc())
                # 提示第二个错误细节
                print("- 临时路径打开错误:\n" + str(e2))
                return 1

        # 导出前 N 页
        out_dir = os.path.join(tempfile.gettempdir(), "ppt_render_output")
        exported = export_first_n_slides(pres, out_dir, first_n=first_n)
        if exported:
            print("渲染成功，导出文件如下：")
            for p in exported:
                print("  ", p)
            return 0
        else:
            print("渲染未导出任何图片（可能无页或导出被安全策略阻止）。")
            return 1
    except Exception:
        print("渲染流程发生异常：\n" + traceback.format_exc())
        return 1
    finally:
        try:
            if pres is not None:
                pres.Close()
        except Exception:
            pass
        try:
            if app is not None:
                app.Quit()
        except Exception:
            pass
        try:
            pythoncom.CoUninitialize()
        except Exception:
            pass


def main():
    parser = argparse.ArgumentParser(description="测试 PowerPoint 文件是否可通过 COM 渲染导出")
    parser.add_argument("ppt_path", help="PPT/PPTX 文件路径")
    parser.add_argument("--first-n", type=int, default=28, help="导出前 N 页，默认 3")
    args = parser.parse_args()

    ppt_path = os.path.abspath(args.ppt_path)
    if not os.path.exists(ppt_path):
        print(f"文件不存在: {ppt_path}")
        sys.exit(1)

    code = test_render_once(ppt_path, first_n=args.first_n)
    sys.exit(code)


if __name__ == "__main__":
    main()


