"""
PPT处理模块
提供PPT文本内容的处理功能，支持并发处理
"""

import os
import logging
import threading
import time
import difflib
import re
import concurrent.futures
import shutil
import json
import datetime
from typing import List, Dict, Any, Optional, Set, Tuple, Callable, Union
from pptx import Presentation
from pptx.shapes.group import GroupShape
from openai import OpenAI
import glob
import random
import tempfile
import math
from copy import deepcopy
from pptx.enum.dml import MSO_FILL

from ..api.api_manager import APIKeyManager
from ..processor.text_processor import TextProcessor
from ..converter.ppt_converter import convert_ppt_to_pptx


class PPTProcessor:
    """
    PPT处理器，负责PPT内容的修改和润色
    """
    
    def __init__(self, 
                 api_key_manager: APIKeyManager, 
                 text_processor: TextProcessor,
                 progress_callback: Optional[Callable[[int, int, int], None]] = None):
        """
        初始化PPT处理器
        
        Args:
            api_key_manager: API密钥管理器
            text_processor: 文本处理器
            progress_callback: 进度回调函数，参数为(progress_percentage, file_index, total_files)
        """
        self.api_key_manager = api_key_manager
        self.text_processor = text_processor
        self.progress_callback = progress_callback
        self.stop_requested = False
        
        # 添加文本修改记录字典，用于记录原始文本和修改后的文本
        # 结构为 {slide_index: {"shapes": [{"original": original_text, "modified": modified_text}]}}
        self.text_changes = {}
        
        # 添加已处理形状ID的集合和递归深度计数，用于防止无限递归
        self.processed_shape_ids = set()
        self.max_recursion_depth = 50  # 增加最大允许递归深度，从20提高到50
    
    def process_presentations(self, 
                             file_paths: List[str], 
                             slide_range: str, 
                             num_modified_files: int, 
                             output_path: str,
                             preview_mode: bool = False) -> List[str]:
        """
        处理多个PPT文件
        
        Args:
            file_paths: PPT文件路径列表
            slide_range: 要处理的幻灯片范围字符串，如"1-5 7 9-12"
            num_modified_files: 每个PPT生成的修改版本数量
            output_path: 输出目录路径
            preview_mode: 是否启用预览模式
            
        Returns:
            处理后的文件路径列表
        """
        modified_file_paths = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths, start=1):
            if self.stop_requested:
                break
                
            logging.info(f"开始处理PPT文件 ({i}/{total_files}): {file_path}")
            
            try:
                # 处理单个PPT文件，并将结果添加到modified_file_paths
                result_paths = self.process_presentation(
                    file_path, slide_range, 
                    1 if preview_mode else num_modified_files, 
                    output_path, i, total_files, preview_mode)
                modified_file_paths.extend(result_paths)
                
            except Exception as e:
                logging.error(f"处理文件失败 {file_path}: {str(e)}")
                continue
        
        return modified_file_paths
    
    def process_presentation(self, 
                            file_path: str, 
                            slide_range: str, 
                            num_modified_files: int, 
                            output_path: str,
                            file_index: int,
                            total_files: int,
                            preview_mode: bool = False) -> List[str]:
        """
        处理单个PPT文件，生成多个修改版本
        
        Args:
            file_path: PPT文件路径
            slide_range: 要处理的幻灯片范围字符串
            num_modified_files: 生成的修改版本数量
            output_path: 输出目录路径
            file_index: 当前文件索引
            total_files: 总文件数
            preview_mode: 是否启用预览模式
            
        Returns:
            处理后的文件路径列表
        """
        logging.info(f"处理演示文稿: {file_path}{' (预览模式)' if preview_mode else ''}")
        
        # 检查文件类型，如果是ppt则先转换
        if file_path.lower().endswith('.ppt'):
            logging.info(f"转换PPT到PPTX: {file_path}")
            pptx_path = convert_ppt_to_pptx(file_path)
            if not pptx_path:
                logging.error(f"无法转换PPT文件: {file_path}")
                return []
            file_path = pptx_path
        
        # 打开PPT文件
        try:
            prs = Presentation(file_path)
        except Exception as e:
            logging.error(f"无法打开PPT文件: {file_path}, 错误: {str(e)}")
            return []
        
        # 解析幻灯片范围
        slides_to_modify = self.parse_slide_range(slide_range, len(prs.slides))
        modified_file_paths = []
        
        # 重置文本修改记录
        self.text_changes = {}
        
        # 在预览模式下，仅生成一个修改版本
        if preview_mode:
            logging.info(f"预览模式: 将生成一个修改版本")
            
            # 构建输出文件路径
            preview_file_name = f"preview_{os.path.basename(file_path)}"
            if preview_file_name.lower().endswith('.ppt'):
                preview_file_name = preview_file_name[:-4] + '.pptx'
            preview_file_path = os.path.join(output_path, preview_file_name)
            
            # 创建修改版本 - 传递preview_mode参数
            if self.create_modified_version(prs, slides_to_modify, preview_file_path, file_index, total_files, preview_mode):
                modified_file_paths.append(preview_file_path)
                logging.info(f"预览文件已生成: {preview_file_path}")
            
            return modified_file_paths
            
        # 获取可用的CPU核心数，增加并发处理的线程数量
        max_workers = min(num_modified_files, os.cpu_count() * 2 or 8)  # 从cpu_count改为cpu_count*2
        logging.info(f"使用{max_workers}个并发线程处理PPT文件")
        
        # 创建线程池来并行处理多个修改版本
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            for i in range(num_modified_files):
                if self.stop_requested:
                    break
                
                # 构建输出文件路径
                modified_file_name = f"modified_{i + 1}_{os.path.basename(file_path)}"
                if modified_file_name.lower().endswith('.ppt'):
                    modified_file_name = modified_file_name[:-4] + '.pptx'
                modified_file_path = os.path.join(output_path, modified_file_name)
                
                # 如果文件已存在，跳过处理
                if os.path.exists(modified_file_path):
                    logging.info(f"跳过 {modified_file_name}, 文件已存在")
                    modified_file_paths.append(modified_file_path)
                    continue
                
                # 提交任务到线程池
                future = executor.submit(
                    self.create_modified_version,
                    prs,
                    slides_to_modify,
                    modified_file_path,
                    file_index,
                    total_files,
                    preview_mode
                )
                futures.append((future, modified_file_path))
            
            # 等待所有任务完成
            for future, path in futures:
                if self.stop_requested:
                    break
                    
                try:
                    if future.result():  # 如果修改成功
                        modified_file_paths.append(path)
                except Exception as e:
                    logging.error(f"创建修改版本时发生错误: {str(e)}")
        
        return modified_file_paths
    
    def create_modified_version(self, 
                               original_prs: Presentation, 
                               slides_to_modify: List[int], 
                               output_path: str,
                               file_index: int,
                               total_files: int,
                               preview_mode: bool = False) -> bool:
        """
        创建单个修改版本
        
        Args:
            original_prs: 原始演示文稿对象
            slides_to_modify: 要修改的幻灯片索引列表
            output_path: 输出文件路径
            file_index: 当前文件索引
            total_files: 总文件数
            preview_mode: 是否为预览模式，预览模式下启用增强的多颜色文本处理
            
        Returns:
            是否成功创建修改版本
        """
        import tempfile
        import os
        
        try:
            # 使用临时文件来避免内存问题
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pptx') as temp_file:
                temp_path = temp_file.name
            
            # 保存原始演示文稿到临时文件
            original_prs.save(temp_path)
            
            # 从临时文件加载新的演示文稿对象
            modified_prs = Presentation(temp_path)
            
            # 修改幻灯片 - 传递preview_mode参数
            self.modify_slides(modified_prs, slides_to_modify, file_index, total_files, preview_mode)
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 保存修改后的演示文稿
            modified_prs.save(output_path)
            logging.info(f"演示文稿已成功修改: {output_path}")
            
            # 删除临时文件
            os.unlink(temp_path)
            return True
            
        except Exception as e:
            logging.error(f"修改版本创建失败: {str(e)}")
            return False
    
    def modify_slides(self, 
                     prs: Presentation, 
                     slides_to_modify: List[int], 
                     file_index: int, 
                     total_files: int,
                     preview_mode: bool = False) -> None:
        """
        修改幻灯片内容
        
        Args:
            prs: 演示文稿对象
            slides_to_modify: 要修改的幻灯片索引列表
            file_index: 当前文件索引
            total_files: 总文件数
            preview_mode: 是否为预览模式，预览模式下启用增强的多颜色文本处理
        """
        # 创建OpenAI客户端
        api_key = self.api_key_manager.get_next_key()
        if not api_key:
            logging.error("没有可用的API密钥，无法处理幻灯片")
            return
            
        # 使用text_processor的方法创建客户端
        client = self.text_processor._create_client(api_key)
        
        total_slides = len(slides_to_modify)
        modified_slides = 0
        
        # 重置文本修改记录
        self.text_changes = {}
        
        # 预览模式下优化线程数量，预览模式时不需要太多并发
        max_workers = min(6 if not preview_mode else 3, os.cpu_count() or 4)
        logging.info(f"使用{max_workers}个并发线程处理幻灯片{' (预览模式)' if preview_mode else ''}")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = []
            
            for slide_index in slides_to_modify:
                if self.stop_requested:
                    break
                
                # 获取幻灯片对象 (索引从0开始，但slide_index从1开始)
                slide = prs.slides[slide_index - 1]
                
                # 将每个幻灯片的处理提交到线程池
                future = executor.submit(
                    self.process_slide, 
                    client, 
                    slide, 
                    file_index, 
                    total_files,
                    slide_index,
                    preview_mode  # 传递预览模式标志
                )
                futures.append((future, slide_index))
            
            # 等待所有任务完成并更新进度
            for future, slide_index in futures:
                if self.stop_requested:
                    break
                    
                try:
                    future.result()  # 获取处理结果
                    modified_slides += 1
                    
                    # 更新进度
                    if self.progress_callback:
                        progress_percentage = int(modified_slides / total_slides * 100)
                        self.progress_callback(progress_percentage, file_index, total_files)
                        
                except Exception as e:
                    logging.error(f"处理幻灯片 {slide_index} 时发生错误: {str(e)}")
                    
        if preview_mode:
            logging.info(f"预览模式：共处理 {modified_slides}/{total_slides} 张幻灯片，记录了 {len(self.text_changes)} 处修改")
    
    def process_slide(self, client: OpenAI, slide: Any, file_index: int, total_files: int, slide_index: int = None, preview_mode: bool = False) -> None:
        """
        处理单张幻灯片，修改其中的文本
        
        Args:
            client: OpenAI客户端
            slide: 幻灯片对象
            file_index: 当前处理的文件索引
            total_files: 总文件数量
            slide_index: 幻灯片索引，用于记录修改
            preview_mode: 是否为预览模式，预览模式下启用增强的多颜色文本处理
        """
        try:
            logging.info(f"开始处理幻灯片 {slide_index}{' (预览模式)' if preview_mode else ''}")
            
            # 在每次处理新幻灯片之前重置已处理形状ID集合，避免状态累积导致递归问题
            self.processed_shape_ids = set()
            
            # 收集要处理的形状
            shapes_to_process = []
            for shape in slide.shapes:
                if shape.has_text_frame or isinstance(shape, GroupShape):
                    shapes_to_process.append(shape)
            
            # 处理每个形状
            for shape in shapes_to_process:
                if self.stop_requested:
                    break
                # 更新API密钥
                api_key = self.api_key_manager.get_next_key()
                if api_key:
                    client.api_key = api_key
                # 处理形状中的文本
                self.modify_text_in_shape(client, shape, slide_index, recursion_depth=0, preview_mode=preview_mode)
                
            logging.info(f"幻灯片 {slide_index} 处理完成")
            
        except Exception as e:
            logging.error(f"处理幻灯片 {slide_index} 时发生异常: {str(e)}")
            
    def modify_text_in_shape(self, client: OpenAI, shape: Any, slide_index: int = None, recursion_depth: int = 0, preview_mode: bool = False) -> None:
        """
        修改形状中的文本
        
        Args:
            client: OpenAI客户端
            shape: 形状对象
            slide_index: 幻灯片索引，用于记录修改
            recursion_depth: 当前递归深度，用于防止无限递归
            preview_mode: 是否为预览模式，预览模式下启用增强的多颜色文本处理
        """
        # 递归深度检查
        if recursion_depth > self.max_recursion_depth:
            logging.warning(f"达到最大递归深度 {self.max_recursion_depth}，停止处理")
            return
            
        # 尝试获取形状ID
        shape_id = None
        try:
            shape_id = id(shape)
            
            # 如果已处理过此形状，则跳过
            if shape_id in self.processed_shape_ids:
                logging.debug(f"跳过已处理过的形状 ID: {shape_id}")
                return
                
            # 添加到已处理集合
            if shape_id is not None:
                self.processed_shape_ids.add(shape_id)
                
        except Exception as e:
            logging.warning(f"获取形状ID时出错: {str(e)}")

        if shape.has_text_frame:
            text_frame = shape.text_frame
            for paragraph in text_frame.paragraphs:
                if self.is_mixed_format(paragraph):
                    # 检查段落中是否包含英文单词
                    has_english = any(bool(re.search(r'[a-zA-Z]+', run.text)) for run in paragraph.runs)
                    if not has_english:
                        # 检查是否为古诗文
                        text = "".join(run.text for run in paragraph.runs)
                        if not self.text_processor.is_classical_chinese(text):
                            # 记录原始文本
                            original_text = text
                            
                            # 获取文本位置信息（如果可能）
                            position = self._get_text_position(shape)
                            
                            # 使用增强的多颜色文本处理方法
                            self.polish_paragraph(client, paragraph)
                            
                            # 记录修改后的文本
                            if slide_index is not None:
                                modified_text = "".join(run.text for run in paragraph.runs)
                                self._record_text_change(slide_index, original_text, modified_text, 
                                                      shape_id=shape_id,
                                                      position=position)
                else:
                    for run in paragraph.runs:
                        # 检查文本中是否包含英文单词
                        if bool(re.search(r'[a-zA-Z]+', run.text)):
                            continue
                        if len(run.text) > self.text_processor.min_text_length and '?' not in run.text and '...' not in run.text and '……' not in run.text:
                            # 检查是否为古诗文
                            if not self.text_processor.is_classical_chinese(run.text):
                                # 记录原始文本
                                original_text = run.text
                                
                                # 获取文本位置信息（如果可能）
                                position = self._get_text_position(shape)
                                
                                # 修改文本
                                polished_text = self.text_processor.polish_text(run.text)
                                if polished_text and polished_text != run.text:
                                    # 记录修改
                                    if slide_index is not None:
                                        self._record_text_change(slide_index, run.text, polished_text, 
                                                             shape_id=shape_id,
                                                             position=position)
                                    # 更新文本
                                    run.text = polished_text
        elif isinstance(shape, GroupShape):
            try:
                # 检查是否有检测到循环引用
                if self._detect_circular_reference(shape):
                    logging.warning(f"检测到循环引用，跳过组合形状 ID: {shape_id}")
                    return
                    
                # 记录已处理的子形状ID，防止在同一组合形状内重复处理
                processed_sub_shapes = set()
                
                # 限制子形状处理数量，防止过深递归
                max_sub_shapes = 50
                shape_count = 0
                
                for sub_shape in shape.shapes:
                    # 限制处理的子形状数量
                    if shape_count >= max_sub_shapes:
                        logging.warning(f"组合形状子项数量超过限制({max_sub_shapes})，跳过剩余子项")
                        break
                        
                    # 跳过已处理的子形状
                    sub_shape_id = id(sub_shape)
                    if sub_shape_id in processed_sub_shapes:
                        logging.debug(f"提取文本时跳过组合形状内的重复子形状: {sub_shape_id}")
                        continue
                    processed_sub_shapes.add(sub_shape_id)
                    
                    self.modify_text_in_shape(client, sub_shape, slide_index, recursion_depth + 1, preview_mode)
                    shape_count += 1
            except Exception as e:
                logging.warning(f"处理组合形状子项时出错: {str(e)}")
    
    def is_mixed_format(self, paragraph: Any) -> bool:
        """
        检查段落是否包含混合格式
        
        Args:
            paragraph: 段落对象
            
        Returns:
            如果包含混合格式返回True，否则返回False
        """
        colors = set()
        for run in paragraph.runs:
            color = None
            if run.font and run.font.color:
                try:
                    color = run.font.color.rgb
                except Exception:
                    color = None
            colors.add(color)
        return len(colors) > 1
    
    def get_color_str(self, run: Any) -> Optional[str]:
        """
        获取文本运行的颜色字符串
        
        Args:
            run: 文本运行对象
            
        Returns:
            颜色字符串，如果没有则返回None
        """
        if not run.font or not run.font.color:
            return None
        try:
            if hasattr(run.font.color, 'rgb') and run.font.color.rgb:
                return str(run.font.color.rgb).upper()
            elif hasattr(run.font.color, 'theme_color') and run.font.color.theme_color:
                return str(run.font.color.theme_color).upper()
        except Exception as e:
            logging.warning(f"获取颜色值失败: {str(e)}")
        return None
    
    def polish_paragraph(self, client: OpenAI, paragraph: Any) -> None:
        """
        整体润色段落，然后根据原始文本比例将润色后的文本分配到各个run中
        参考T_9.py中的实现，加强了多颜色文本处理能力
        
        Args:
            client: OpenAI客户端
            paragraph: 段落对象
        """
        # 检查段落是否为空
        if not paragraph.runs:
            logging.info("段落为空，跳过处理")
            return
            
        # 构建token信息，每个token对应paragraph中的一个run
        token_info = []
        for run in paragraph.runs:
            color_str = self.get_color_str(run)
            token_info.append({"color": color_str, "text": run.text})
        
        # 检查总文本是否为空
        total_text = "".join(token["text"] for token in token_info)
        if not total_text.strip():
            logging.info("段落文本为空，跳过处理")
            return
            
        # 检查文本中是否已经包含特殊标记，如果有则跳过处理以防止递归问题
        if "[[COLOR_" in total_text and "]]" in total_text:
            logging.warning(f"文本中已存在特殊标记，跳过处理以防止递归: {total_text[:50]}...")
            return
            
        # 拼接时对有颜色的token加上保护标记，采用带索引的标记格式
        protected_text = ""
        for index, token in enumerate(token_info):
            if token["color"] is not None:
                protected_text += f"[[COLOR_{index}:{token['color']}]]{token['text']}[[ENDCOLOR_{index}]]"
            else:
                protected_text += token["text"]
        
        # 检查生成的保护文本长度是否异常
        if len(protected_text) > len(total_text) * 3:
            logging.warning(f"生成的保护文本异常长度: {len(protected_text)}，原文本长度: {len(total_text)}，跳过处理")
            return
            
        # 追加说明，要求严格保留新格式的标记，不要拆分、合并或删除标记，且确保标记之间没有多余的空格
        note = "\n请严格保留[[COLOR_x:...]]和[[ENDCOLOR_x]]标记，不要拆分、合并或删除标记，且请确保标记之间没有多余的空格。"
        protected_text += note
        
        # 检查保护标记数量是否一致
        start_markers_count = protected_text.count("[[COLOR_")
        end_markers_count = protected_text.count("[[ENDCOLOR_")
        if start_markers_count != end_markers_count:
            logging.warning(f"保护标记数量不一致: 开始标记 {start_markers_count}，结束标记 {end_markers_count}，使用原文")
            return
            
        # 调用润色接口对整体带标记内容进行润色
        try:
            polished_protected_text = self.text_processor.polish_text(protected_text)
            if polished_protected_text is None or polished_protected_text == protected_text:
                logging.warning("润色段落失败或未发生变化，保留原文")
                return
        except Exception as e:
            logging.error(f"调用润色API时发生错误: {str(e)}")
            return
        
        # 检查保护标记是否匹配，最多重试3次
        attempt = 1
        max_attempts = 3
        while True:
            found_markers = re.findall(r'\[\[COLOR_(\d+):', polished_protected_text)
            end_markers = re.findall(r'\[\[ENDCOLOR_(\d+)\]\]', polished_protected_text)
            
            # 更简单的检查方法：确保标记存在且匹配
            if found_markers and end_markers and sorted(found_markers) == sorted(end_markers):
                # 检查每对标记是否正确嵌套 - 使用更简单的逻辑
                valid = True
                for index in found_markers:
                    start_marker = f"[[COLOR_{index}:"
                    end_marker = f"[[ENDCOLOR_{index}]]"
                    start_pos = polished_protected_text.find(start_marker)
                    end_pos = polished_protected_text.find(end_marker)
                    
                    if start_pos == -1 or end_pos == -1 or start_pos > end_pos:
                        valid = False
                        break
                
                if valid:
                    break  # 标记完全匹配，退出循环
            
            # 如果已达到最大尝试次数，则放弃处理
            if attempt >= max_attempts:
                logging.error("经过多次重试后，保护标记仍然不匹配，返回原始文本")
                return
                
            # 重试润色
            logging.warning(f"保护标记不匹配，重试润色: 尝试 {attempt}/{max_attempts}")
            try:
                polished_protected_text = self.text_processor.polish_text(protected_text)
                if polished_protected_text is None:
                    return
            except Exception as e:
                logging.error(f"重试润色API时发生错误: {str(e)}")
                return
                
            attempt += 1
        
        # 移除附加的说明文字
        polished_protected_text = polished_protected_text.replace(note, "")
        
        # 提取润色后的有颜色部分，使用正则提取新的标记格式 [[COLOR_index:颜色]]文本[[ENDCOLOR_index]]
        regex_pattern = r'\[\[COLOR_(\d+):([^\]]+)\]\](.*?)\[\[ENDCOLOR_\1\]\]'
        try:
            colored_matches = re.findall(regex_pattern, polished_protected_text, re.DOTALL)
            colored_dict = {}
            for match in colored_matches:
                index = int(match[0])
                colored_dict[index] = match[2]
        except Exception as e:
            logging.error(f"提取颜色标记时发生错误: {str(e)}")
            return
        
        # 去掉保护标记，得到无颜色部分的整体润色文本
        try:
            polished_clean = re.sub(regex_pattern, '', polished_protected_text, flags=re.DOTALL)
        except Exception as e:
            logging.error(f"去除颜色标记时发生错误: {str(e)}")
            return
        
        # 对于无颜色token，计算所有无颜色token的原始总长度，并构建累计长度数组
        noncolored_tokens = [token for token in token_info if token["color"] is None]
        
        # 检查是否有无颜色的文本
        if not noncolored_tokens:
            # 如果没有无颜色的文本，直接分配有颜色的部分
            try:
                for i, run in enumerate(paragraph.runs):
                    if token_info[i]["color"] is not None and i in colored_dict:
                        run.text = colored_dict[i]
            except Exception as e:
                logging.error(f"分配有颜色文本时发生错误: {str(e)}")
            return
            
        total_noncolored_length = sum(len(token["text"]) for token in noncolored_tokens)
        
        # 处理总长度为0的特殊情况
        if total_noncolored_length == 0:
            try:
                for i, run in enumerate(paragraph.runs):
                    if token_info[i]["color"] is not None and i in colored_dict:
                        run.text = colored_dict[i]
                    else:
                        run.text = ""  # 空文本保持空
            except Exception as e:
                logging.error(f"处理无颜色空文本时发生错误: {str(e)}")
            return
            
        cumulative = [0]
        for token in noncolored_tokens:
            cumulative.append(cumulative[-1] + len(token["text"]))
        
        # 使用difflib对齐，将原始无颜色文本映射到润色后的文本
        orig_noncolored = ''.join(token["text"] for token in noncolored_tokens)
        
        # 确保polished_clean不为空
        if not polished_clean:
            polished_clean = orig_noncolored
            
        # 使用SequenceMatcher进行更精确的文本对齐
        sm = difflib.SequenceMatcher(None, orig_noncolored, polished_clean)
        opcodes = sm.get_opcodes()
        
        # 类似T_9.py的映射函数，但增加了边界检查和异常处理
        def map_index(x):
            if x >= total_noncolored_length:
                return len(polished_clean)
            for tag, i1, i2, j1, j2 in opcodes:
                if i1 <= x < i2:
                    if tag == "equal":
                        return j1 + (x - i1)
                    else:
                        if i2 != i1:
                            ratio = (x - i1) / (i2 - i1)
                            return j1 + int(ratio * (j2 - j1))
                        else:
                            return j1
            return len(polished_clean)
        
        try:
            new_noncolored_texts = []
            for i in range(len(noncolored_tokens)):
                start_idx_orig = cumulative[i]
                end_idx_orig = cumulative[i + 1]
                new_start = map_index(start_idx_orig)
                new_end = map_index(end_idx_orig)
                
                # 确保索引在有效范围内
                new_start = max(0, min(new_start, len(polished_clean)))
                new_end = max(0, min(new_end, len(polished_clean)))
                
                new_segment = polished_clean[new_start:new_end]
                if not new_segment:
                    new_segment = noncolored_tokens[i]["text"]
                new_noncolored_texts.append(new_segment)
            
            # 将新文本按原顺序还原到各个run中
            noncolored_index = 0
            for i, run in enumerate(paragraph.runs):
                if token_info[i]["color"] is not None:
                    # 有颜色的文本从colored_dict获取
                    if i in colored_dict:
                        run.text = colored_dict[i]
                    # 如果未在字典中找到，保留原文本
                else:
                    # 无颜色的文本从new_noncolored_texts获取
                    if noncolored_index < len(new_noncolored_texts):
                        run.text = new_noncolored_texts[noncolored_index]
                        noncolored_index += 1
                    else:
                        run.text = token_info[i]["text"]
        except Exception as e:
            logging.error(f"分配润色文本时发生错误: {str(e)}")
            # 出错时保留原文
            for i, run in enumerate(paragraph.runs):
                run.text = token_info[i]["text"]
    
    def parse_slide_range(self, range_str: str, total_slides: int) -> List[int]:
        """
        解析幻灯片范围字符串
        
        Args:
            range_str: 范围字符串，如"1-5 7 9-12"
            total_slides: 总幻灯片数
            
        Returns:
            幻灯片索引列表
        """
        if not range_str:
            # 默认范围: 跳过第一页，处理其余所有页
            return list(range(2, total_slides + 1))
        
        slides = []
        for part in range_str.split():
            if '-' in part:
                start, end = map(int, part.split('-'))
                slides.extend(range(start, end + 1))
            else:
                slides.append(int(part))
        
        # 确保所有索引都在有效范围内
        return [s for s in slides if 1 <= s <= total_slides]
    
    def request_stop(self) -> None:
        """请求停止处理"""
        self.stop_requested = True 
    
    def _record_text_change(self, slide_index: int, original_text: str, modified_text: str, shape_id=None, text_range=None, position=None) -> None:
        """
        记录文本修改，增强版本
        
        Args:
            slide_index: 幻灯片索引
            original_text: 原始文本
            modified_text: 修改后的文本
            shape_id: 形状ID（可选）
            text_range: 文本范围信息（可选）
            position: 文本在幻灯片中的位置信息 (x, y, width, height)
        """
        if slide_index not in self.text_changes:
            self.text_changes[slide_index] = {"changes": []}
        
        self.text_changes[slide_index]["changes"].append({
            "original": original_text,
            "modified": modified_text,
            "shape_id": shape_id,
            "text_range": text_range,
            "position": position,
            "accepted": True  # 默认接受
        })
    
    def get_text_changes(self) -> Dict[int, Dict[str, List[Dict[str, str]]]]:
        """
        获取文本修改记录
        
        Returns:
            文本修改记录字典
        """
        return self.text_changes 
    
    def apply_selective_changes(self, 
                              original_path: str, 
                              output_path: str, 
                              change_decisions: Dict[int, Dict[str, bool]],
                              slides_to_modify: List[int]) -> str:
        """
        根据用户选择，选择性地应用文本修改
        
        Args:
            original_path: 原始PPT路径
            output_path: 输出路径
            change_decisions: 用户决策字典 {slide_index: {change_id: is_accepted}}
            slides_to_modify: 要修改的幻灯片索引列表
            
        Returns:
            创建的文件路径
        """
        # 基本检查
        if not os.path.exists(original_path):
            error_msg = f"原始文件不存在: {original_path}"
            logging.error(error_msg)
            raise FileNotFoundError(error_msg)
            
        # 从preview文件名推断出preview文件路径
        preview_path = None
        
        # 检查output_path是否以_selective.pptx结尾
        if output_path.endswith("_selective.pptx"):
            # 获取不带_selective的文件路径作为preview文件
            preview_path = output_path.replace("_selective.pptx", ".pptx")
            
            # 检查文件是否存在
            if os.path.exists(preview_path):
                logging.info(f"找到preview文件: {preview_path}")
            else:
                # 尝试另一种命名方式: 检查是否有带preview_前缀的文件
                dir_name = os.path.dirname(output_path)
                base_name = os.path.basename(original_path)
                alt_preview_path = os.path.join(dir_name, f"preview_{base_name}")
                
                if os.path.exists(alt_preview_path):
                    preview_path = alt_preview_path
                    logging.info(f"找到替代preview文件: {preview_path}")
                else:
                    logging.warning(f"无法找到preview文件: {preview_path} 或 {alt_preview_path}，将使用原始文件")
                    preview_path = original_path
        else:
            # 如果output_path不符合预期格式，尝试构造preview路径
            dir_name = os.path.dirname(output_path)
            base_name = os.path.basename(original_path)
            preview_path = os.path.join(dir_name, f"preview_{base_name}")
            
            # 检查文件是否存在
            if os.path.exists(preview_path):
                logging.info(f"找到preview文件: {preview_path}")
            else:
                # 尝试不带preview_前缀的文件名
                alt_preview_path = os.path.join(dir_name, base_name.replace('.ppt', '.pptx'))
                if os.path.exists(alt_preview_path):
                    preview_path = alt_preview_path
                    logging.info(f"找到替代preview文件: {preview_path}")
                else:
                    logging.warning(f"无法找到preview文件: {preview_path} 或 {alt_preview_path}，将使用原始文件")
                    preview_path = original_path
        
        # 最终检查
        if not os.path.exists(preview_path):
            error_msg = f"无法找到有效的preview文件，无法继续处理"
            logging.error(error_msg)
            raise FileNotFoundError(error_msg)
            
        # 检查output_path的目录是否存在，如果不存在则创建
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
                logging.info(f"创建输出目录: {output_dir}")
            except Exception as e:
                error_msg = f"无法创建输出目录 {output_dir}: {str(e)}"
                logging.error(error_msg)
                raise IOError(error_msg)
                
        # 检查是否有足够修改记录
        if not self.text_changes:
            warning_msg = "没有找到文本修改记录，selective文件可能与原始文件相同"
            logging.warning(warning_msg)
            
        # 初始化失败页面属性
        self.failed_pages = set()
            
        # 调用简化版的selective应用方法
        try:
            result = self.simple_selective_changes(preview_path, output_path, change_decisions)
            # simple_selective_changes方法中已经设置了self.failed_pages
            return result
        except Exception as e:
            error_msg = f"应用selective修改失败: {str(e)}"
            logging.error(error_msg)
            raise RuntimeError(error_msg)
    
    def simple_selective_changes(self,
                              preview_path: str,
                              output_path: str,
                              change_decisions: Dict[int, Dict[str, bool]]) -> str:
        """简化版的选择性应用修改
        
        这个方法是apply_selective_changes的简化版，直接在preview文件上应用选择性修改
        
        Args:
            preview_path: 预览版PPT的路径
            output_path: 输出路径
            change_decisions: 修改决策 {slide_index: {change_id: accept}}
            
        Returns:
            输出文件路径
        """
        logging.info(f"开始简化版选择性应用修改: {preview_path} -> {output_path}")
        
        # 统计信息
        total_changes = 0
        accepted_changes = 0
        rejected_changes = 0
        failed_changes = 0
        # 跟踪失败发生的页面
        failed_pages = set()
        # 跟踪需要完全使用原始幻灯片的页面
        use_original_slides = set()
        
        # 为了方便其他组件获取失败页面信息，保存到实例属性中
        self.failed_pages = failed_pages
        
        # 检查是否有幻灯片被标记为使用原始版本
        for slide_idx, decisions in change_decisions.items():
            # 检查是否有任何修改项被标记为"use_original"
            for change_id, decision in decisions.items():
                if decision == "use_original":
                    use_original_slides.add(slide_idx)
                    logging.info(f"幻灯片 {slide_idx} 被标记为使用原始版本")
                    break
        
        # 尝试查找原始文件
        original_file = self._find_original_file_from_preview(preview_path)
        
        if original_file and os.path.exists(original_file):
            logging.info(f"找到关联的原始文件: {original_file}")
            
            try:
                # 打开原始文件
                original_prs = Presentation(original_file)
                logging.info(f"成功打开原始文件: {original_file}")
                
                # 打开预览文件
                preview_prs = Presentation(preview_path)
                
                # 创建副本用于输出
                shutil.copy2(preview_path, output_path)
                result_prs = Presentation(output_path)
                
                # 找到有修改的幻灯片
                modified_slides = set(self.text_changes.keys())
                logging.info(f"找到 {len(modified_slides)} 张幻灯片有修改记录")
                
                # 按幻灯片处理修改
                for slide_idx in modified_slides:
                    slide_changes = self.text_changes.get(slide_idx, {}).get('changes', [])
                    decisions = change_decisions.get(slide_idx, {})
                    
                    if not slide_changes:
                        continue
                    
                    logging.info(f"处理幻灯片 {slide_idx}，共有 {len(slide_changes)} 项修改")
                    
                    # 获取对应幻灯片索引
                    slide_index_0 = slide_idx - 1  # 转为0开始的索引
                    
                    # 获取原始幻灯片
                    if slide_index_0 < len(original_prs.slides):
                        original_slide = original_prs.slides[slide_index_0]
                    else:
                        logging.warning(f"原始文件中找不到幻灯片 {slide_idx}")
                        continue
                    
                    # 获取预览幻灯片
                    if slide_index_0 < len(preview_prs.slides):
                        preview_slide = preview_prs.slides[slide_index_0]
                    else:
                        logging.warning(f"预览文件中找不到幻灯片 {slide_idx}")
                        continue
                    
                    # 获取结果幻灯片
                    if slide_index_0 < len(result_prs.slides):
                        result_slide = result_prs.slides[slide_index_0]
                    else:
                        logging.warning(f"结果文件中找不到幻灯片 {slide_idx}")
                        continue
                    
                    # 检查此幻灯片是否需要使用原始版本
                    if slide_idx in use_original_slides:
                        logging.info(f"幻灯片 {slide_idx} 使用原始版本")
                        try:
                            # 完全复制原始幻灯片的内容到结果幻灯片
                            self._copy_slide_content(original_slide, result_slide)
                            logging.info(f"成功复制原始幻灯片 {slide_idx} 的内容")
                            continue  # 跳过后续处理
                        except Exception as e:
                            logging.error(f"复制原始幻灯片 {slide_idx} 失败: {str(e)}")
                            failed_pages.add(slide_idx)
                            continue
                    
                    # 收集原始幻灯片中的所有文本形状
                    original_shapes = []
                    self._collect_all_shapes_with_text(original_slide, original_shapes)
                    logging.debug(f"在原始幻灯片 {slide_idx} 中找到 {len(original_shapes)} 个文本形状")
                    
                    # 收集结果幻灯片中的所有文本形状
                    result_shapes = []
                    self._collect_all_shapes_with_text(result_slide, result_shapes)
                    
                    # 处理每个修改项
                    for i, change in enumerate(slide_changes):
                        change_id = f"{slide_idx}_{i}"
                        total_changes += 1
                        accept = decisions.get(change_id, True)  # 修改为默认接受
                        
                        # 跳过被标记为"use_original"的修改项
                        if accept == "use_original":
                            continue
                        
                        original_text = change.get('original', '')
                        modified_text = change.get('modified', '')
                        position = change.get('position')
                        
                        if not accept:
                            # 如果修改被拒绝，需要还原为原始文本（包括格式）
                            rejected_changes += 1
                            logging.info(f"拒绝修改项 {change_id}，尝试还原为原始内容")
                            
                            # 尝试从原始文件中复制内容（包括格式）
                            if original_shapes:
                                # 尝试在原始文件中查找匹配的文本对象
                                if self._copy_text_with_format(original_shapes, result_shapes, original_text, modified_text, position):
                                    logging.info(f"成功从原始文件复制文本和格式: 修改项 {change_id}")
                                    continue
                                else:
                                    logging.warning(f"无法在原始文件中找到匹配的文本: 修改项 {change_id}，将尝试只还原文本内容")
                            
                            # 如果没有原始文件或无法找到匹配的文本对象，退回到只还原文本内容
                            if self._restore_original_text(result_shapes, modified_text, original_text):
                                logging.info(f"已还原修改项 {change_id} 为原始文本（可能丢失格式）")
                            else:
                                logging.warning(f"无法找到修改项 {change_id} 对应的文本进行还原")
                                failed_changes += 1
                                failed_pages.add(slide_idx)
                            
                            continue
                        
                        # 接受修改
                        accepted_changes += 1
                        logging.info(f"接受修改项 {change_id}，修改后文本: '{modified_text}'")
                        
                        # 检查是否有手动编辑的文本
                        if change.get('manually_edited'):
                            logging.info(f"检测到用户手动修改的文本: '{modified_text}'")
                            
                            # 策略1: 尝试使用 AI 修改版本查找文本
                            ai_modified = change.get('ai_modified', '')
                            if ai_modified:
                                logging.info(f"策略1: 使用AI修改版本 '{ai_modified[:50]}{'...' if len(ai_modified)>50 else ''}' 查找文本")
                                logging.info(f"手动修改的文本: '{modified_text}'")
                                logging.info(f"原始文本: '{original_text}'")
                                
                                # 尝试使用模糊查找AI修改版本
                                logging.info(f"尝试使用模糊查找AI修改版本...")
                                if self._fuzzy_update_text(result_shapes, ai_modified, modified_text):
                                    continue
                                
                                # 如果模糊查找失败，尝试使用新的直接查找方法
                                logging.info(f"尝试使用直接查找AI修改版本...")
                                if self._find_and_update_text_direct(result_slide.shapes, ai_modified, modified_text):
                                    continue
                            
                            # 策略2: 尝试使用原始文本查找
                            logging.info(f"策略2: 尝试使用原始文本 '{original_text[:50]}{'...' if len(original_text)>50 else ''}' 查找并替换")
                            
                            # 尝试对原始文本做模糊查找
                            logging.info(f"尝试对原始文本做模糊查找...")
                            if self._fuzzy_update_text(result_shapes, original_text, modified_text):
                                continue
                                
                            # 如果模糊查找失败，尝试使用新的直接查找方法
                            logging.info(f"尝试对原始文本做直接查找...")
                            if self._find_and_update_text_direct(result_slide.shapes, original_text, modified_text):
                                continue
                            
                            # 策略3: 尝试直接查找相似文本
                            logging.info(f"策略3: 尝试直接查找相似文本")
                            if self._fuzzy_update_text(result_shapes, modified_text, modified_text, find_similar=True):
                                continue
                                
                            # 如果所有文本策略失败，尝试基于位置
                            logging.warning(f"所有文本匹配策略都失败，尝试基于位置匹配...")
                            if position and self._update_text_by_position(result_slide, position, modified_text):
                                continue
                                
                            logging.warning(f"无法应用手动修改的文本: 修改项 {change_id}")
                            failed_changes += 1
                            failed_pages.add(slide_idx)
                            
                        else:
                            # 常规修改 - 直接接受预览文件中的内容
                            logging.info(f"接受常规AI修改项 {change_id}，保留预览文件中的内容")
                            # 因为是在预览文件的副本上操作，预览文件中已包含AI修改后的内容，无需再次查找替换
                            continue
                            
                            # 以下代码保留但不执行，用于特殊情况的调试或未来需要时可以恢复
                            """
                            # 常规修改，尝试使用原始文本查找
                            if self._fuzzy_update_text(result_shapes, original_text, modified_text):
                                continue
                                
                            # 如果模糊查找失败，尝试使用新的直接查找方法
                            if self._find_and_update_text_direct(result_slide.shapes, original_text, modified_text):
                                continue
                                
                            # 如果文本策略失败，尝试基于位置
                            if position and self._update_text_by_position(result_slide, position, modified_text):
                                continue
                                
                            logging.warning(f"无法应用修改: 修改项 {change_id}")
                            failed_changes += 1
                            """
                
                # 保存结果
                result_prs.save(output_path)
                logging.info(f"已保存简化版选择性修改后的PPT: {output_path}")
                # 打印统计信息
                failed_pages_str = ", ".join([str(page) for page in sorted(failed_pages)]) if failed_pages else "无"
                logging.info(f"总计: {total_changes}项修改，接受: {accepted_changes}项，拒绝: {rejected_changes}项，失败: {failed_changes}项，失败页面: {failed_pages_str}")
                return output_path
                
            except Exception as e:
                logging.error(f"应用选择性修改时出错: {str(e)}")
                import traceback
                logging.error(traceback.format_exc())
                return preview_path
                
        else:
            # 如果找不到原始文件，直接在预览版上进行修改
            logging.warning("未找到原始文件，直接在预览版上进行修改")
            
            try:
                # 创建副本用于输出
                shutil.copy2(preview_path, output_path)
                result_prs = Presentation(output_path)
                
                # 找到有修改的幻灯片
                modified_slides = set(self.text_changes.keys())
                
                # 按幻灯片处理修改
                for slide_idx in modified_slides:
                    slide_changes = self.text_changes.get(slide_idx, {}).get('changes', [])
                    decisions = change_decisions.get(slide_idx, {})
                    
                    if not slide_changes:
                        continue
                    
                    logging.info(f"处理幻灯片 {slide_idx}，共有 {len(slide_changes)} 项修改")
                    
                    # 获取对应幻灯片索引
                    slide_index_0 = slide_idx - 1  # 转为0开始的索引
                    
                    # 获取结果幻灯片
                    if slide_index_0 < len(result_prs.slides):
                        result_slide = result_prs.slides[slide_index_0]
                    else:
                        logging.warning(f"结果文件中找不到幻灯片 {slide_idx}")
                        continue
                    
                    # 收集结果幻灯片中的所有文本形状
                    result_shapes = []
                    self._collect_all_shapes_with_text(result_slide, result_shapes)
                    
                    # 处理每个修改项
                    for i, change in enumerate(slide_changes):
                        change_id = f"{slide_idx}_{i}"
                        total_changes += 1
                        accept = decisions.get(change_id, True)  # 修改为默认接受
                        
                        original_text = change.get('original', '')
                        modified_text = change.get('modified', '')
                        position = change.get('position')
                        
                        if not accept:
                            # 修改被拒绝，但找不到原始文件，只能尝试简单文本还原
                            rejected_changes += 1
                            logging.warning(f"拒绝修改项 {change_id}，但找不到原始文件，尝试简单文本还原")
                            
                            if self._restore_original_text(result_shapes, modified_text, original_text):
                                logging.info(f"已还原修改项 {change_id} 为原始文本（可能丢失格式）")
                            else:
                                logging.warning(f"无法找到修改项 {change_id} 对应的文本进行还原")
                                failed_changes += 1
                                failed_pages.add(slide_idx)
                            
                            continue
                        
                        # 接受修改
                        accepted_changes += 1
                        logging.info(f"接受修改项 {change_id}，修改后文本: '{modified_text}'")
                        
                        # 先尝试文本替换
                        if self._fuzzy_update_text(result_shapes, original_text, modified_text):
                            continue
                            
                        # 如果模糊查找失败，尝试使用新的直接查找方法
                        if self._find_and_update_text_direct(result_slide.shapes, original_text, modified_text):
                            continue
                            
                        # 如果文本替换失败，尝试基于位置
                        if position and self._update_text_by_position(result_slide, position, modified_text):
                            continue
                            
                        logging.warning(f"无法应用修改: 修改项 {change_id}")
                        failed_changes += 1
                        failed_pages.add(slide_idx)
                
                # 保存结果
                result_prs.save(output_path)
                logging.info(f"已保存简化版选择性修改后的PPT: {output_path}")
                # 打印统计信息
                failed_pages_str = ", ".join([str(page) for page in sorted(failed_pages)]) if failed_pages else "无"
                logging.info(f"总计: {total_changes}项修改，接受: {accepted_changes}项，拒绝: {rejected_changes}项，失败: {failed_changes}项，失败页面: {failed_pages_str}")
                return output_path
                
            except Exception as e:
                logging.error(f"应用选择性修改时出错: {str(e)}")
                import traceback
                logging.error(traceback.format_exc())
                return preview_path
    
    def _find_original_file_from_preview(self, preview_path: str) -> Optional[str]:
        """
        从preview文件路径推断原始文件路径
        
        Args:
            preview_path: preview文件路径
            
        Returns:
            推断的原始文件路径，如果找不到则返回None
        """
        logging.info(f"尝试从预览文件路径推断原始文件路径: {preview_path}")
        
        # 1. 尝试从文件名中推断 - 移除"preview_"前缀和时间戳后缀
        file_name = os.path.basename(preview_path)
        dir_path = os.path.dirname(preview_path)
        dir_name = os.path.basename(dir_path)
        parent_dir = os.path.dirname(dir_path)
        
        # 如果文件名以preview_开头
        if file_name.startswith("preview_"):
            # 使用正则表达式提取原始文件名
            # 精确匹配格式：preview_原始文件名_YYYYMMDD_HHMMSS.pptx
            original_name_pattern = r'^preview_(.+?)_\d{8}_\d{6}\.(pptx?)'
            match = re.match(original_name_pattern, file_name)
            
            if match:
                # 直接从文件名中提取原始文件名
                original_name = match.group(1) + "." + match.group(2)
                logging.info(f"从preview文件名提取的原始文件名: {original_name}")
                
                # 尝试1: 在当前目录查找
                original_path = os.path.join(dir_path, original_name)
                if os.path.exists(original_path):
                    logging.info(f"在当前目录找到原始文件: {original_path}")
                    return original_path
                
                # 尝试2: 查找不同扩展名
                alternate_ext = '.ppt' if original_name.endswith('.pptx') else '.pptx'
                test_path = original_path.replace('.pptx', alternate_ext).replace('.ppt', alternate_ext)
                if os.path.exists(test_path):
                    logging.info(f"在当前目录找到不同扩展名的原始文件: {test_path}")
                    return test_path
                
                # 尝试3: 在父目录查找
                parent_path = os.path.join(parent_dir, original_name)
                if os.path.exists(parent_path):
                    logging.info(f"在父目录找到原始文件: {parent_path}")
                    return parent_path
            else:
                # 如果正则匹配不成功，使用更简单的切分方法
                # 移除preview_前缀
                name_without_prefix = file_name[8:]  # 移除"preview_"
                
                # 查找时间戳部分（_YYYYMMDD_HHMMSS）
                timestamp_pattern = r'_\d{8}_\d{6}\.'
                match = re.search(timestamp_pattern, name_without_prefix)
                
                if match:
                    # 移除时间戳
                    timestamp_start = match.start()
                    ext_start = name_without_prefix.rfind('.')
                    if ext_start > timestamp_start:
                        original_name = name_without_prefix[:timestamp_start] + name_without_prefix[ext_start:]
                        logging.info(f"通过简单切分提取的原始文件名: {original_name}")
                        
                        # 在当前目录查找
                        original_path = os.path.join(dir_path, original_name)
                        if os.path.exists(original_path):
                            logging.info(f"在当前目录找到原始文件: {original_path}")
                            return original_path
                        
                        # 在父目录查找
                        parent_path = os.path.join(parent_dir, original_name)
                        if os.path.exists(parent_path):
                            logging.info(f"在父目录找到原始文件: {parent_path}")
                            return parent_path
                else:
                    # 没有时间戳，可能是其他格式，保留原逻辑
                    # 去除时间戳部分 (_YYYYMMDD_HHMMSS)
                    timestamp_pattern = r'_\d{8}_\d{6}'
                    cleaned_name = re.sub(timestamp_pattern, '', name_without_prefix)
                    
                    original_path = os.path.join(dir_path, cleaned_name)
                    if os.path.exists(original_path):
                        logging.info(f"在当前目录找到原始文件: {original_path}")
                        return original_path
                    
                    # 尝试不同扩展名
                    for ext in ['.pptx', '.ppt']:
                        test_path = original_path.replace('.pptx', ext).replace('.ppt', ext)
                        if os.path.exists(test_path):
                            logging.info(f"在当前目录找到不同扩展名的原始文件: {test_path}")
                            return test_path
        
        # 2. 检查是否在输出文件夹(XX-Y)中，尝试找到对应的输入文件夹(folder_XX)
        if '-' in dir_name:
            parts = dir_name.split('-')
            if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                folder_num = parts[0]
                input_folder_name = f"folder_{folder_num}"
                input_folder_path = os.path.join(parent_dir, input_folder_name)
                
                if os.path.exists(input_folder_path) and os.path.isdir(input_folder_path):
                    logging.info(f"识别到预览文件位于输出文件夹 {dir_name}，对应的输入文件夹为 {input_folder_name}")
                    
                    # 准备在输入文件夹中查找的文件名
                    original_name = None
                    if file_name.startswith("preview_"):
                        # 使用正则表达式提取原始文件名
                        original_name_pattern = r'^preview_(.+?)_\d{8}_\d{6}\.(pptx?)'
                        match = re.match(original_name_pattern, file_name)
                        
                        if match:
                            original_name = match.group(1) + "." + match.group(2)
                        else:
                            # 移除preview_前缀
                            name_without_prefix = file_name[8:]
                            
                            # 查找时间戳部分（_YYYYMMDD_HHMMSS）
                            timestamp_pattern = r'_\d{8}_\d{6}\.'
                            match = re.search(timestamp_pattern, name_without_prefix)
                            
                            if match:
                                # 移除时间戳
                                timestamp_start = match.start()
                                ext_start = name_without_prefix.rfind('.')
                                if ext_start > timestamp_start:
                                    original_name = name_without_prefix[:timestamp_start] + name_without_prefix[ext_start:]
                    else:
                        original_name = file_name
                    
                    if original_name:
                        # 在输入文件夹中查找原始文件
                        test_path = os.path.join(input_folder_path, original_name)
                        if os.path.exists(test_path):
                            logging.info(f"在输入文件夹中找到精确匹配的原始文件: {test_path}")
                            return test_path
                        
                        # 尝试不同扩展名
                        for ext in ['.pptx', '.ppt']:
                            test_path = os.path.join(input_folder_path, original_name.replace('.pptx', ext).replace('.ppt', ext))
                            if os.path.exists(test_path):
                                logging.info(f"在输入文件夹中找到不同扩展名的原始文件: {test_path}")
                                return test_path
        
        # 如果之前的尝试都失败了，保留原来的后备方案
        # 3. 检查是否在输入文件夹(folder_XX)中
        if dir_name.startswith("folder_") and dir_name[7:].isdigit():
            folder_num = dir_name[7:]
            # 尝试查找对应的输出文件夹
            output_pattern = os.path.join(parent_dir, f"{folder_num}-*")
            output_folders = glob.glob(output_pattern)
            
            if output_folders:
                # 按照数字部分排序，找出最新的输出文件夹
                sorted_outputs = sorted(output_folders, 
                                      key=lambda p: int(os.path.basename(p).split('-')[1]) 
                                      if os.path.basename(p).split('-')[1].isdigit() else 0,
                                      reverse=True)
                latest_output = sorted_outputs[0]
                
                # 在最新的输出文件夹中查找匹配的预览文件
                original_name = None
                if file_name.startswith("preview_"):
                    # 使用正则表达式提取原始文件名
                    original_name_pattern = r'^preview_(.+?)_\d{8}_\d{6}\.(pptx?)'
                    match = re.match(original_name_pattern, file_name)
                    
                    if match:
                        original_name = match.group(1) + "." + match.group(2)
                    else:
                        # 使用简单切分方法
                        # 移除preview_前缀
                        name_without_prefix = file_name[8:]
                        
                        # 查找时间戳部分（_YYYYMMDD_HHMMSS）
                        timestamp_pattern = r'_\d{8}_\d{6}\.'
                        match = re.search(timestamp_pattern, name_without_prefix)
                        
                        if match:
                            # 移除时间戳
                            timestamp_start = match.start()
                            ext_start = name_without_prefix.rfind('.')
                            if ext_start > timestamp_start:
                                original_name = name_without_prefix[:timestamp_start] + name_without_prefix[ext_start:]
                
                if original_name:
                    # 尝试找到原始文件
                    original_path = os.path.join(latest_output, original_name)
                    if os.path.exists(original_path):
                        logging.info(f"在对应的输出文件夹中找到原始文件: {original_path}")
                        return original_path
        
        # 如果所有尝试都失败，返回None
        logging.warning(f"无法找到与预览文件对应的原始文件: {preview_path}")
        return None
    
    def _copy_text_with_format(self, original_shapes, preview_shapes, original_text, modified_text, position=None):
        """
        从原始形状中找到匹配的文本，并将其完整复制到目标形状中
        
        Args:
            original_shapes: 原始文档中的形状列表
            preview_shapes: preview文档中的形状列表
            original_text: 原始文本
            modified_text: 修改后的文本
            position: 文本位置信息（可选）
            
        Returns:
            是否成功复制
        """
        # 步骤1: 在原始形状中找到包含原始文本的形状
        original_shape = None
        original_paragraph = None
        original_run = None
        
        for shape in original_shapes:
            if not hasattr(shape, 'text_frame'):
                continue
                
            text_frame = shape.text_frame
            
            # 检查整个文本框
            if text_frame.text.strip() == original_text.strip():
                original_shape = shape
                break
                
            # 检查每个段落
            for p in text_frame.paragraphs:
                if "".join(run.text for run in p.runs).strip() == original_text.strip():
                    original_shape = shape
                    original_paragraph = p
                    break
                    
                # 检查每个run
                for run in p.runs:
                    if run.text.strip() == original_text.strip():
                        original_shape = shape
                        original_paragraph = p
                        original_run = run
                        break
                        
                if original_paragraph:
                    break
                    
            if original_shape:
                break
        
        if not original_shape:
            logging.warning(f"在原始文档中找不到匹配的文本: '{original_text[:30]}...'")
            return False
            
        # 步骤2: 在preview形状中找到包含修改文本的形状
        preview_shape = None
        preview_paragraph = None
        preview_run = None
        
        for shape in preview_shapes:
            if not hasattr(shape, 'text_frame'):
                continue
                
            text_frame = shape.text_frame
            
            # 检查整个文本框
            if text_frame.text.strip() == modified_text.strip():
                preview_shape = shape
                break
                
            # 检查每个段落
            for p in text_frame.paragraphs:
                if "".join(run.text for run in p.runs).strip() == modified_text.strip():
                    preview_shape = shape
                    preview_paragraph = p
                    break
                    
                # 检查每个run
                for run in p.runs:
                    if run.text.strip() == modified_text.strip():
                        preview_shape = shape
                        preview_paragraph = p
                        preview_run = run
                        break
                        
                if preview_paragraph:
                    break
                    
            if preview_shape:
                break
        
        if not preview_shape:
            logging.warning(f"在preview文档中找不到匹配的文本: '{modified_text[:30]}...'")
            return False
            
        # 步骤3: 复制文本内容和格式
        # 根据找到的级别（形状、段落或run）执行不同的复制策略
        
        try:
            # 情况1: 完整的文本框
            if original_shape and preview_shape and not original_paragraph and not preview_paragraph:
                # 复制整个文本框
                self._copy_text_frame_content(original_shape.text_frame, preview_shape.text_frame)
                return True
                
            # 情况2: 特定段落
            elif original_paragraph and preview_paragraph:
                # 复制段落内容和格式
                self._copy_paragraph_content(original_paragraph, preview_paragraph)
                return True
                
            # 情况3: 特定文本run
            elif original_run and preview_run:
                # 复制run内容和格式
                self._copy_run_format(original_run, preview_run)
                return True
                
            # 情况4: 混合情况，尝试最佳匹配
            else:
                if original_shape and preview_shape:
                    # 复制整个文本框，这是最保险的做法
                    self._copy_text_frame_content(original_shape.text_frame, preview_shape.text_frame)
                    return True
                    
                logging.warning("无法确定准确的复制级别，复制失败")
                return False
                
        except Exception as e:
            logging.error(f"复制文本格式时发生错误: {str(e)}")
            return False
    
    def _copy_text_frame_content(self, source_frame, target_frame):
        """
        复制整个文本框的内容和格式
        
        Args:
            source_frame: 源文本框
            target_frame: 目标文本框
        """
        # 清空目标文本框中的所有段落
        while len(target_frame.paragraphs) > 1:
            p = target_frame.paragraphs[-1]
            tr = p._element
            tr.getparent().remove(tr)
        
        # 清空第一个段落的内容
        if len(target_frame.paragraphs) > 0:
            p = target_frame.paragraphs[0]
            p.clear()
        
        # 复制文本框属性
        if hasattr(source_frame, 'margin_left') and hasattr(target_frame, 'margin_left'):
            target_frame.margin_left = source_frame.margin_left
        if hasattr(source_frame, 'margin_right') and hasattr(target_frame, 'margin_right'):
            target_frame.margin_right = source_frame.margin_right
        if hasattr(source_frame, 'margin_top') and hasattr(target_frame, 'margin_top'):
            target_frame.margin_top = source_frame.margin_top
        if hasattr(source_frame, 'margin_bottom') and hasattr(target_frame, 'margin_bottom'):
            target_frame.margin_bottom = source_frame.margin_bottom
        if hasattr(source_frame, 'vertical_anchor') and hasattr(target_frame, 'vertical_anchor'):
            target_frame.vertical_anchor = source_frame.vertical_anchor
        if hasattr(source_frame, 'word_wrap') and hasattr(target_frame, 'word_wrap'):
            target_frame.word_wrap = source_frame.word_wrap
        if hasattr(source_frame, 'auto_size') and hasattr(target_frame, 'auto_size'):
            target_frame.auto_size = source_frame.auto_size
            
        # 复制每个段落
        first_paragraph = True
        for src_p in source_frame.paragraphs:
            if first_paragraph and len(target_frame.paragraphs) > 0:
                # 第一个段落直接使用目标中已有的段落
                tgt_p = target_frame.paragraphs[0]
                first_paragraph = False
            else:
                # 添加新段落
                tgt_p = target_frame.add_paragraph()
            
            # 复制段落属性
            if hasattr(src_p, 'alignment') and hasattr(tgt_p, 'alignment'):
                tgt_p.alignment = src_p.alignment
            if hasattr(src_p, 'level') and hasattr(tgt_p, 'level'):
                tgt_p.level = src_p.level
            if hasattr(src_p, 'line_spacing') and hasattr(tgt_p, 'line_spacing'):
                tgt_p.line_spacing = src_p.line_spacing
            if hasattr(src_p, 'space_before') and hasattr(tgt_p, 'space_before'):
                tgt_p.space_before = src_p.space_before
            if hasattr(src_p, 'space_after') and hasattr(tgt_p, 'space_after'):
                tgt_p.space_after = src_p.space_after
                
            # 复制每个run
            for src_run in src_p.runs:
                tgt_run = tgt_p.add_run()
                tgt_run.text = src_run.text
                
                # 复制run的格式
                self._copy_run_format(src_run, tgt_run)
    
    def _copy_paragraph_content(self, source_paragraph, target_paragraph):
        """
        复制段落的内容和格式
        
        Args:
            source_paragraph: 源段落
            target_paragraph: 目标段落
        """
        # 清空目标段落的所有run
        for run in list(target_paragraph.runs):
            run.text = ""
            
        # 复制段落属性
        if hasattr(source_paragraph, 'alignment') and hasattr(target_paragraph, 'alignment'):
            target_paragraph.alignment = source_paragraph.alignment
        if hasattr(source_paragraph, 'level') and hasattr(target_paragraph, 'level'):
            target_paragraph.level = source_paragraph.level
        if hasattr(source_paragraph, 'line_spacing') and hasattr(target_paragraph, 'line_spacing'):
            target_paragraph.line_spacing = source_paragraph.line_spacing
        if hasattr(source_paragraph, 'space_before') and hasattr(target_paragraph, 'space_before'):
            target_paragraph.space_before = source_paragraph.space_before
        if hasattr(source_paragraph, 'space_after') and hasattr(target_paragraph, 'space_after'):
            target_paragraph.space_after = source_paragraph.space_after
            
        # 复制每个run
        for src_run in source_paragraph.runs:
            tgt_run = target_paragraph.add_run()
            tgt_run.text = src_run.text
            
            # 复制run的格式
            self._copy_run_format(src_run, tgt_run)
    
    def _copy_run_format(self, source_run, target_run):
        """
        复制run的格式
        
        Args:
            source_run: 源run
            target_run: 目标run
        """
        target_run.text = source_run.text
        
        # 复制字体格式
        if hasattr(source_run, 'font') and hasattr(target_run, 'font'):
            src_font = source_run.font
            tgt_font = target_run.font
            
            # 复制基本属性
            if hasattr(src_font, 'bold') and hasattr(tgt_font, 'bold'):
                tgt_font.bold = src_font.bold
            if hasattr(src_font, 'italic') and hasattr(tgt_font, 'italic'):
                tgt_font.italic = src_font.italic
            if hasattr(src_font, 'underline') and hasattr(tgt_font, 'underline'):
                tgt_font.underline = src_font.underline
            if hasattr(src_font, 'strike') and hasattr(tgt_font, 'strike'):
                tgt_font.strike = src_font.strike
            if hasattr(src_font, 'shadow') and hasattr(tgt_font, 'shadow'):
                tgt_font.shadow = src_font.shadow
            if hasattr(src_font, 'size') and hasattr(tgt_font, 'size'):
                tgt_font.size = src_font.size
            if hasattr(src_font, 'name') and hasattr(tgt_font, 'name'):
                tgt_font.name = src_font.name
                
            # 复制颜色
            if hasattr(src_font, 'color') and hasattr(tgt_font, 'color'):
                if hasattr(src_font.color, 'rgb') and src_font.color.rgb:
                    tgt_font.color.rgb = src_font.color.rgb
                if hasattr(src_font.color, 'theme_color') and src_font.color.theme_color:
                    tgt_font.color.theme_color = src_font.color.theme_color
    
    def _restore_original_text(self, shapes, modified_text, original_text):
        """
        在形状列表中查找匹配的修改后文本并还原为原始文本
        
        Args:
            shapes: 形状列表
            modified_text: 修改后的文本
            original_text: 原始文本
            
        Returns:
            是否成功还原
        """
        for shape in shapes:
            if not shape.has_text_frame:
                continue
                
            text_frame = shape.text_frame
            
            # 检查整个文本框的文本
            if text_frame.text.strip() == modified_text.strip():
                logging.debug(f"找到完全匹配的文本框: '{modified_text[:30]}...'")
                
                # 保存第一个段落，以保留其格式
                if len(text_frame.paragraphs) > 0:
                    p = text_frame.paragraphs[0]
                    
                    # 如果有多个运行，将它们合并为一个
                    if len(p.runs) > 0:
                        # 保存第一个运行的格式
                        first_run = p.runs[0]
                        
                        # 清除段落中的所有文本，但保留运行
                        for run in p.runs:
                            run.text = ""
                            
                        # 将原始文本设置到第一个运行中
                        first_run.text = original_text
                    else:
                        # 如果没有运行，则添加一个
                        p.text = original_text
                    
                    # 删除多余的段落
                    while len(text_frame.paragraphs) > 1:
                        try:
                            extra_p = text_frame.paragraphs[-1]
                            tr = extra_p._element
                            tr.getparent().remove(tr)
                        except Exception as e:
                            logging.warning(f"删除多余段落时出错: {str(e)}")
                            break
                else:
                    # 如果没有段落，添加一个
                    text_frame.text = original_text
                    
                return True
                
            # 逐段落检查
            for paragraph in text_frame.paragraphs:
                paragraph_text = "".join(run.text for run in paragraph.runs).strip()
                if paragraph_text == modified_text.strip():
                    logging.debug(f"找到匹配的段落: '{modified_text[:30]}...'")
                    
                    # 保留第一个运行，以保留其格式
                    if len(paragraph.runs) > 0:
                        # 保存第一个运行
                        first_run = paragraph.runs[0]
                        
                        # 清除所有运行中的文本
                        for run in paragraph.runs:
                            run.text = ""
                            
                        # 将原始文本设置到第一个运行中
                        first_run.text = original_text
                        
                        # 删除多余的运行
                        while len(paragraph.runs) > 1:
                            try:
                                extra_run = paragraph.runs[-1]
                                r = extra_run._element
                                r.getparent().remove(r)
                            except Exception as e:
                                logging.warning(f"删除多余运行时出错: {str(e)}")
                                break
                    else:
                        # 如果没有运行，则添加一个
                        paragraph.text = original_text
                        
                    return True
                    
                # 逐运行检查
                for i, run in enumerate(paragraph.runs):
                    if run.text.strip() == modified_text.strip():
                        logging.debug(f"找到匹配的运行: '{modified_text[:30]}...'")
                        run.text = original_text
                        return True
                        
        logging.warning(f"未找到匹配的文本: '{modified_text[:30]}...'")
        return False
    
    def validate_and_fix_position(self, position: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证位置数据的合理性并修正异常值
        
        Args:
            position: 原始位置信息
            
        Returns:
            验证和修正后的位置信息
        """
        if not position:
            return position
            
        # 创建新的位置字典，避免修改原始对象
        fixed_position = position.copy()
        slide_width = fixed_position.get('slide_width', 9144000)
        slide_height = fixed_position.get('slide_height', 6858000)
        
        # 检查并修正绝对坐标
        if fixed_position.get('x', 0) < 0:
            logging.warning(f"发现异常的X坐标: {fixed_position.get('x')}, 修正为0")
            fixed_position['x'] = 0
        elif fixed_position.get('x', 0) > slide_width * 2:
            logging.warning(f"发现异常的X坐标: {fixed_position.get('x')}, 修正为最大值的两倍")
            fixed_position['x'] = slide_width * 2
            
        if fixed_position.get('y', 0) < 0:
            logging.warning(f"发现异常的Y坐标: {fixed_position.get('y')}, 修正为0")
            fixed_position['y'] = 0
        elif fixed_position.get('y', 0) > slide_height * 2:
            logging.warning(f"发现异常的Y坐标: {fixed_position.get('y')}, 修正为最大值的两倍")
            fixed_position['y'] = slide_height * 2
            
        # 检查并修正宽度和高度
        if fixed_position.get('width', 0) <= 0:
            logging.warning(f"发现异常的宽度: {fixed_position.get('width')}, 修正为合理值")
            fixed_position['width'] = slide_width * 0.1  # 使用10%的幻灯片宽度作为默认值
        elif fixed_position.get('width', 0) > slide_width * 3:
            logging.warning(f"发现异常的宽度: {fixed_position.get('width')}, 修正为最大值的三倍")
            fixed_position['width'] = slide_width * 3
            
        if fixed_position.get('height', 0) <= 0:
            logging.warning(f"发现异常的高度: {fixed_position.get('height')}, 修正为合理值")
            fixed_position['height'] = slide_height * 0.05  # 使用5%的幻灯片高度作为默认值
        elif fixed_position.get('height', 0) > slide_height * 3:
            logging.warning(f"发现异常的高度: {fixed_position.get('height')}, 修正为最大值的三倍")
            fixed_position['height'] = slide_height * 3
            
        # 重新计算相对位置
        fixed_position['relative_x'] = fixed_position['x'] / slide_width
        fixed_position['relative_y'] = fixed_position['y'] / slide_height
        fixed_position['relative_width'] = fixed_position['width'] / slide_width
        fixed_position['relative_height'] = fixed_position['height'] / slide_height
        
        # 检查相对位置的合理性
        if fixed_position['relative_x'] < 0 or fixed_position['relative_x'] > 3:
            logging.warning(f"计算得到异常的相对X坐标: {fixed_position['relative_x']}, 进行修正")
            fixed_position['relative_x'] = max(0, min(fixed_position['relative_x'], 3))
            fixed_position['x'] = fixed_position['relative_x'] * slide_width
            
        if fixed_position['relative_y'] < 0 or fixed_position['relative_y'] > 3:
            logging.warning(f"计算得到异常的相对Y坐标: {fixed_position['relative_y']}, 进行修正")
            fixed_position['relative_y'] = max(0, min(fixed_position['relative_y'], 3))
            fixed_position['y'] = fixed_position['relative_y'] * slide_height
            
        if fixed_position['relative_width'] <= 0 or fixed_position['relative_width'] > 3:
            logging.warning(f"计算得到异常的相对宽度: {fixed_position['relative_width']}, 进行修正")
            fixed_position['relative_width'] = max(0.01, min(fixed_position['relative_width'], 3))
            fixed_position['width'] = fixed_position['relative_width'] * slide_width
            
        if fixed_position['relative_height'] <= 0 or fixed_position['relative_height'] > 3:
            logging.warning(f"计算得到异常的相对高度: {fixed_position['relative_height']}, 进行修正")
            fixed_position['relative_height'] = max(0.01, min(fixed_position['relative_height'], 3))
            fixed_position['height'] = fixed_position['relative_height'] * slide_height
            
        return fixed_position
        
    def _get_text_position(self, shape: Any) -> Optional[Dict[str, float]]:
        """
        获取文本形状的位置信息
        
        Args:
            shape: 形状对象
            
        Returns:
            包含位置信息的字典 {x, y, width, height} 或 None
        """
        try:
            # 获取幻灯片尺寸信息
            slide_width = 9144000  # 默认EMU单位
            slide_height = 6858000  # 默认4:3比例
            
            # 尝试从幻灯片中获取实际尺寸
            if hasattr(shape, 'part') and hasattr(shape.part, 'slide_layouts'):
                try:
                    slide_layout = shape.part.slide_layouts[0]
                    if hasattr(slide_layout, 'width') and hasattr(slide_layout, 'height'):
                        slide_width = slide_layout.width
                        slide_height = slide_layout.height
                        logging.debug(f"获取到幻灯片实际尺寸: width={slide_width}, height={slide_height}")
                except Exception as layout_err:
                    logging.debug(f"获取幻灯片布局尺寸失败: {str(layout_err)}")
            
            # 尝试获取形状的基本属性
            if hasattr(shape, 'left') and hasattr(shape, 'top') and hasattr(shape, 'width') and hasattr(shape, 'height'):
                # 获取绝对位置
                left = shape.left
                top = shape.top
                width = shape.width
                height = shape.height
                
                # 计算相对位置（相对于幻灯片尺寸的比例）
                relative_x = left / slide_width
                relative_y = top / slide_height
                relative_width = width / slide_width
                relative_height = height / slide_height
                
                # 记录获取的形状位置信息
                logging.debug(f"获取形状位置: left={left}, top={top}, width={width}, height={height}")
                logging.debug(f"相对位置: x={relative_x:.4f}, y={relative_y:.4f}, w={relative_width:.4f}, h={relative_height:.4f}")
                
                # 获取形状旋转信息（如果有）
                rotation = 0
                if hasattr(shape, 'rotation'):
                    rotation = shape.rotation
                    logging.debug(f"形状旋转角度: {rotation}度")
                
                # 构建完整的位置信息字典
                position = {
                    "x": left,
                    "y": top,
                    "width": width,
                    "height": height,
                    "relative_x": relative_x,
                    "relative_y": relative_y,
                    "relative_width": relative_width,
                    "relative_height": relative_height,
                    "slide_width": slide_width,
                    "slide_height": slide_height,
                    "rotation": rotation
                }
                
                # 验证和修正位置信息
                position = self.validate_and_fix_position(position)
                
                return position
        except Exception as e:
            logging.warning(f"获取形状位置失败: {str(e)}")
        
        # 如果无法获取精确位置，返回一个更合理的估计位置
        # 基于常见的PPT文本位置估计
        estimated_slide_width = 9144000  # 默认EMU单位
        estimated_slide_height = 6858000  # 默认4:3比例
        
        # 估计位置为幻灯片中心偏上位置
        estimated_x = estimated_slide_width * 0.1   # 距左边10%
        estimated_y = estimated_slide_height * 0.2  # 距顶部20%
        estimated_width = estimated_slide_width * 0.8  # 宽度80%
        estimated_height = estimated_slide_height * 0.1  # 高度10%
        
        estimated_position = {
            "x": estimated_x,
            "y": estimated_y,
            "width": estimated_width,
            "height": estimated_height,
            "relative_x": 0.1,  # 相对位置
            "relative_y": 0.2,
            "relative_width": 0.8,
            "relative_height": 0.1,
            "slide_width": estimated_slide_width,
            "slide_height": estimated_slide_height,
            "rotation": 0
        }
        
        # 验证和修正估计的位置信息
        estimated_position = self.validate_and_fix_position(estimated_position)
        
        return estimated_position
    
    def extract_slide_texts(self, slide) -> List[Dict[str, Any]]:
        """
        提取幻灯片中的所有文本及其位置信息
        
        Args:
            slide: 幻灯片对象
            
        Returns:
            包含文本和位置信息的字典列表 [{text: str, position: {x, y, width, height}}]
        """
        text_items = []
        
        # 清空已处理形状ID集合
        self.processed_shape_ids = set()
        
        # 遍历幻灯片上的所有形状
        for shape in slide.shapes:
            # 处理文本框
            if hasattr(shape, "text") and shape.text.strip():
                position = self._get_text_position(shape)
                text_items.append({
                    "text": shape.text.strip(),
                    "position": position
                })
            
            # 处理表格
            elif shape.has_table:
                for row in shape.table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            # 对于表格单元格，尝试获取其在幻灯片中的近似位置
                            position = self._get_text_position(shape)  # 使用表格的位置作为近似
                            text_items.append({
                                "text": cell.text.strip(),
                                "position": position
                            })
            
            # 处理组合形状
            elif shape.shape_type == 6:  # GROUP
                # 为每个组合形状创建一个新的已处理形状ID集合
                group_texts = self.extract_texts_from_group(shape, recursion_depth=0)
                text_items.extend(group_texts)
        
        return text_items
    
    def extract_texts_from_group(self, group_shape, recursion_depth: int = 0, parent_offset_x: int = 0, parent_offset_y: int = 0) -> List[Dict[str, Any]]:
        """
        从组合形状中提取文本及其位置信息
        
        Args:
            group_shape: 组合形状对象
            recursion_depth: 当前递归深度
            parent_offset_x: 父组合形状的X偏移量
            parent_offset_y: 父组合形状的Y偏移量
            
        Returns:
            包含文本和位置信息的字典列表
        """
        text_items = []
        
        # 检查递归深度
        if recursion_depth > self.max_recursion_depth:
            logging.warning(f"提取文本时递归深度超过限制({self.max_recursion_depth})，停止处理组合形状")
            return text_items
            
        # 检测循环引用
        if recursion_depth > 0 and self._detect_circular_reference(group_shape):
            logging.warning("提取文本时检测到循环引用，跳过处理该组合形状")
            return text_items
        
        # 获取形状ID并检查是否已处理过
        shape_id = None
        try:
            if hasattr(group_shape, 'shape_id'):
                shape_id = group_shape.shape_id
            elif hasattr(group_shape, 'id'):
                shape_id = group_shape.id
            else:
                # 如果没有ID，尝试使用对象的内存地址作为唯一标识
                shape_id = id(group_shape)
                
            # 如果已处理过此形状，则跳过
            if shape_id in self.processed_shape_ids:
                logging.debug(f"提取文本时跳过已处理过的组合形状 ID: {shape_id}")
                return text_items
                
            # 添加到已处理集合
            if shape_id is not None:
                self.processed_shape_ids.add(shape_id)
                
        except Exception as e:
            logging.warning(f"提取文本时获取形状ID出错: {str(e)}")
        
        # 获取当前组合形状的偏移量
        group_offset_x = 0
        group_offset_y = 0
        try:
            if hasattr(group_shape, 'left'):
                group_offset_x = group_shape.left
            if hasattr(group_shape, 'top'):
                group_offset_y = group_shape.top
            
            # 累加父组合形状的偏移量
            group_offset_x += parent_offset_x
            group_offset_y += parent_offset_y
            
            logging.debug(f"组合形状偏移量: x={group_offset_x}, y={group_offset_y}")
        except Exception as e:
            logging.warning(f"获取组合形状偏移量失败: {str(e)}")
        
        try:
            processed_sub_shapes = set()
            # 限制子形状处理数量，防止过深递归
            max_sub_shapes = 100
            shape_count = 0
            
            for shape in group_shape.shapes:
                # 限制处理的子形状数量
                if shape_count >= max_sub_shapes:
                    logging.warning(f"提取文本时组合形状子项数量超过限制({max_sub_shapes})，跳过剩余子项")
                    break
                    
                # 跳过已处理的子形状
                sub_shape_id = id(shape)
                if sub_shape_id in processed_sub_shapes:
                    logging.debug(f"提取文本时跳过组合形状内的重复子形状: {sub_shape_id}")
                    continue
                processed_sub_shapes.add(sub_shape_id)
                
                # 处理文本框
                if hasattr(shape, "text") and shape.text.strip():
                    # 获取基本位置信息
                    base_position = self._get_text_position(shape)
                    
                    # 应用组合形状偏移量
                    position = self._apply_group_offset(base_position, group_offset_x, group_offset_y)
                    
                    text_items.append({
                        "text": shape.text.strip(),
                        "position": position
                    })
                
                # 处理表格
                elif hasattr(shape, "has_table") and shape.has_table:
                    for row in shape.table.rows:
                        for cell in row.cells:
                            if cell.text.strip():
                                # 获取基本位置信息
                                base_position = self._get_text_position(shape)
                                
                                # 应用组合形状偏移量
                                position = self._apply_group_offset(base_position, group_offset_x, group_offset_y)
                                
                                text_items.append({
                                    "text": cell.text.strip(),
                                    "position": position
                                })
                
                # 处理嵌套的组合形状 - 传递累计的偏移量
                elif isinstance(shape, GroupShape) or shape.shape_type == 6:  # GROUP
                    nested_texts = self.extract_texts_from_group(
                        shape, 
                        recursion_depth + 1,
                        group_offset_x,  # 传递累计的偏移量
                        group_offset_y
                    )
                    text_items.extend(nested_texts)
                    
                shape_count += 1
        except Exception as e:
            logging.warning(f"处理组合形状时发生错误: {str(e)}")
            
        return text_items
    
    def _apply_group_offset(self, position: Dict[str, Any], offset_x: int, offset_y: int) -> Dict[str, Any]:
        """
        应用组合形状偏移量到位置信息
        
        Args:
            position: 原始位置信息
            offset_x: X偏移量
            offset_y: Y偏移量
            
        Returns:
            应用偏移量后的位置信息
        """
        if not position:
            return position
            
        # 创建新的位置字典，避免修改原始对象
        new_position = position.copy()
        
        # 应用偏移量到绝对坐标
        new_position['x'] += offset_x
        new_position['y'] += offset_y
        
        # 重新计算相对坐标
        slide_width = new_position.get('slide_width', 9144000)
        slide_height = new_position.get('slide_height', 6858000)
        
        new_position['relative_x'] = new_position['x'] / slide_width
        new_position['relative_y'] = new_position['y'] / slide_height
        
        # 应用位置验证与修正
        new_position = self.validate_and_fix_position(new_position)
        
        return new_position
    
    def save_processing_results(self, file_path: str, output_dir: str = None) -> str:
        """
        保存处理结果到文件，以便后续离线预览
        
        Args:
            file_path: 原始PPT文件路径
            output_dir: 输出目录，如果为None则使用原文件所在目录
            
        Returns:
            保存的处理结果文件路径
        """
        if not self.text_changes:
            logging.warning("没有处理结果可保存")
            return None
            
        # 确定输出目录
        if output_dir is None:
            output_dir = os.path.dirname(file_path)
            
        os.makedirs(output_dir, exist_ok=True)
        
        # 构建保存文件名
        base_name = os.path.basename(file_path)
        file_name_without_ext = os.path.splitext(base_name)[0]
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = os.path.join(output_dir, f"{file_name_without_ext}_processing_results_{timestamp}.json")
        
        # 准备保存数据
        data_to_save = {
            "original_file": file_path,
            "timestamp": timestamp,
            "text_changes": self.text_changes,
            "metadata": {
                "total_changes": sum(len(slide_data["changes"]) for slide_data in self.text_changes.values()),
                "slides_modified": list(self.text_changes.keys())
            }
        }
        
        # 保存到文件
        try:
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
            logging.info(f"处理结果已保存到: {result_file}")
            return result_file
        except Exception as e:
            logging.error(f"保存处理结果时发生错误: {str(e)}")
            return None
    
    def load_processing_results(self, result_file: str) -> bool:
        """
        从文件加载处理结果，用于离线预览
        
        Args:
            result_file: 处理结果文件路径
            
        Returns:
            是否成功加载
        """
        if not os.path.exists(result_file):
            logging.error(f"处理结果文件不存在: {result_file}")
            return False
            
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 检查数据格式
            if "text_changes" not in data:
                logging.error(f"无效的处理结果文件格式: {result_file}")
                return False
                
            # 更新文本修改记录
            self.text_changes = data["text_changes"]
            
            # 将字符串键转换为整数键（JSON会将所有键转为字符串）
            self.text_changes = {int(k): v for k, v in self.text_changes.items()}
            
            logging.info(f"成功加载处理结果: {result_file}")
            logging.info(f"加载了 {data['metadata']['total_changes']} 个修改项，涉及 {len(data['metadata']['slides_modified'])} 张幻灯片")
            
            return True
        except Exception as e:
            logging.error(f"加载处理结果时发生错误: {str(e)}")
            return False
    
    def process_for_offline_preview(self, 
                                   file_path: str, 
                                   slide_range: str, 
                                   output_dir: str = None) -> Tuple[str, str]:
        """
        处理PPT文件并保存结果，用于后续离线预览
        
        Args:
            file_path: PPT文件路径
            slide_range: 要处理的幻灯片范围
            output_dir: 输出目录
            
        Returns:
            包含两个元素的元组: (保存的结果文件路径, 生成的预览PPT文件路径)
        """
        logging.info(f"开始处理PPT用于离线预览: {file_path}")
        
        # 检查文件类型，如果是ppt则先转换
        if file_path.lower().endswith('.ppt'):
            logging.info(f"转换PPT到PPTX: {file_path}")
            pptx_path = convert_ppt_to_pptx(file_path)
            if not pptx_path:
                logging.error(f"无法转换PPT文件: {file_path}")
                return None, None
            file_path = pptx_path
        
        # 确定输出目录
        if output_dir is None:
            output_dir = os.path.dirname(file_path)
        os.makedirs(output_dir, exist_ok=True)
        
        # 构建预览PPT的基本文件名（不含时间戳）
        base_name = os.path.basename(file_path)
        file_name_without_ext = os.path.splitext(base_name)[0]
        
        # 检查输出目录中是否已存在相应的preview文件
        existing_preview_files = []
        preview_base_pattern = f"preview_{file_name_without_ext}"
        
        try:
            # 获取输出目录中所有文件
            for f in os.listdir(output_dir):
                if f.startswith(preview_base_pattern) and f.lower().endswith('.pptx'):
                    existing_preview_path = os.path.join(output_dir, f)
                    existing_preview_files.append(existing_preview_path)
            
            # 如果找到已存在的preview文件
            if existing_preview_files:
                # 按文件修改时间排序，取最新的
                existing_preview_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                latest_preview_file = existing_preview_files[0]
                
                logging.info(f"在输出文件夹中找到已存在的preview文件: {latest_preview_file}，跳过处理")
                
                # 尝试找到关联的JSON文件
                json_file = self.find_associated_json_file(latest_preview_file)
                
                # 如果找到了JSON文件，加载处理结果
                if json_file and os.path.exists(json_file):
                    self.load_processing_results(json_file)
                    return json_file, latest_preview_file
                
                # 如果没有找到关联的JSON文件，则创建一个
                logging.info(f"未找到关联的JSON文件，将为现有preview文件创建新的处理结果")
                
                # 初始化text_changes以便生成JSON文件
                # 打开PPT文件
                try:
                    prs = Presentation(file_path)
                    # 解析幻灯片范围
                    slides_to_modify = self.parse_slide_range(slide_range, len(prs.slides))
                    # 重置文本修改记录
                    self.text_changes = {}
                    # 保存处理结果
                    result_file = self.save_processing_results(file_path, output_dir)
                    
                    # 更新JSON文件中的preview_file字段
                    if result_file:
                        try:
                            with open(result_file, 'r', encoding='utf-8') as f:
                                data = json.load(f)
                            data["preview_file"] = latest_preview_file
                            with open(result_file, 'w', encoding='utf-8') as f:
                                json.dump(data, f, ensure_ascii=False, indent=2)
                        except Exception as e:
                            logging.warning(f"更新JSON文件关联失败: {str(e)}")
                    
                    return result_file, latest_preview_file
                except Exception as e:
                    logging.error(f"处理现有preview文件时出错: {str(e)}")
        except Exception as e:
            logging.warning(f"检查现有preview文件时出错: {str(e)}")
        
        # 如果没有找到现有文件或处理出错，继续正常流程
        logging.info(f"未找到现有preview文件或处理失败，将创建新的preview文件")
        
        # 打开PPT文件
        try:
            prs = Presentation(file_path)
        except Exception as e:
            logging.error(f"无法打开PPT文件: {file_path}, 错误: {str(e)}")
            return None, None
        
        # 解析幻灯片范围
        slides_to_modify = self.parse_slide_range(slide_range, len(prs.slides))
        
        # 重置文本修改记录
        self.text_changes = {}
        
        # 构建带时间戳的预览PPT文件路径
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        preview_file_name = f"preview_{file_name_without_ext}_{timestamp}.pptx"
        preview_file_path = os.path.join(output_dir, preview_file_name)
        
        # 创建修改版本，同时记录修改
        if not self.create_modified_version(prs, slides_to_modify, preview_file_path, 1, 1):
            logging.error("创建预览PPT文件失败")
            return None, None
        
        # 保存处理结果到JSON文件
        result_file = self.save_processing_results(file_path, output_dir)
        if not result_file:
            logging.error("保存处理结果到JSON文件失败")
            return None, preview_file_path
        
        # 为JSON和PPT文件建立关联 - 在JSON中添加预览文件的信息
        try:
            with open(result_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 添加预览文件路径
            data["preview_file"] = preview_file_path
            
            # 重新保存JSON文件
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 在预览PPT中添加自定义属性，记录关联的JSON文件
            try:
                preview_prs = Presentation(preview_file_path)
                # 检查和创建自定义文档属性以存储JSON文件路径
                if not hasattr(preview_prs.core_properties, 'comments') or preview_prs.core_properties.comments is None:
                    preview_prs.core_properties.comments = ""
                # 在注释中存储关联的JSON文件路径
                preview_prs.core_properties.comments = f"JSON_FILE:{result_file}"
                preview_prs.save(preview_file_path)
            except Exception as e:
                logging.warning(f"在预览PPT中添加自定义属性失败: {str(e)}")
        except Exception as e:
            logging.warning(f"更新JSON文件以关联预览PPT失败: {str(e)}")
        
        logging.info(f"完成离线预览处理: JSON={result_file}, PPT={preview_file_path}")
        return result_file, preview_file_path

    def find_associated_json_file(self, preview_ppt_path: str) -> Optional[str]:
        """
        查找与预览PPT文件关联的JSON文件
        
        Args:
            preview_ppt_path: 预览PPT文件路径
            
        Returns:
            关联的JSON文件路径，如果找不到则返回None
        """
        # 尝试从PPT文件的自定义属性中读取
        try:
            prs = Presentation(preview_ppt_path)
            if hasattr(prs.core_properties, 'comments') and prs.core_properties.comments:
                comments = prs.core_properties.comments
                if comments.startswith("JSON_FILE:"):
                    json_file = comments[10:].strip()
                    if os.path.exists(json_file):
                        logging.info(f"从PPT属性中找到关联的JSON文件: {json_file}")
                        return json_file
        except Exception as e:
            logging.warning(f"从PPT读取自定义属性失败: {str(e)}")
        
        # 如果无法从PPT属性中读取，尝试从文件名和位置推断
        try:
            # 从文件名中提取时间戳
            file_name = os.path.basename(preview_ppt_path)
            if not file_name.startswith("preview_"):
                return None
            
            # 移除"preview_"前缀和扩展名
            base_part = file_name[8:-5]  # 移除"preview_"和".pptx"
            
            # 检查是否包含时间戳格式（20YYMMDD_HHMMSS）
            timestamp_pattern = r'\d{8}_\d{6}'
            match = re.search(timestamp_pattern, base_part)
            
            if match:
                timestamp = match.group(0)
                # 推断可能的JSON文件名
                dir_path = os.path.dirname(preview_ppt_path)
                # 提取基本文件名（不含timestamp）
                file_base = base_part[:base_part.find(timestamp) - 1] if "_" + timestamp in base_part else base_part.replace(timestamp, "")
                
                # 构建可能的JSON文件名
                potential_json = os.path.join(dir_path, f"{file_base}_processing_results_{timestamp}.json")
                if os.path.exists(potential_json):
                    logging.info(f"根据文件名推断找到关联的JSON文件: {potential_json}")
                    return potential_json
        
            # 如果找不到精确匹配，在同一目录下查找所有JSON文件
            dir_path = os.path.dirname(preview_ppt_path)
            json_files = [f for f in os.listdir(dir_path) if f.endswith('.json') and 'processing_results' in f]
            
            # 检查这些JSON文件中是否有引用这个预览文件的
            for json_file in json_files:
                json_path = os.path.join(dir_path, json_file)
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if data.get("preview_file") == preview_ppt_path:
                            logging.info(f"在JSON文件中找到对预览文件的引用: {json_path}")
                            return json_path
                except Exception:
                    continue
        except Exception as e:
            logging.warning(f"尝试推断关联的JSON文件失败: {str(e)}")
        
        logging.warning(f"无法找到与预览文件关联的JSON文件: {preview_ppt_path}")
        return None 

    def _detect_circular_reference(self, shape: Any, parent_shapes: Set[int] = None) -> bool:
        """
        检测形状是否存在循环引用关系
        
        Args:
            shape: 要检测的形状对象
            parent_shapes: 已知的父级形状ID集合
            
        Returns:
            如果检测到循环引用返回True，否则返回False
        """
        if parent_shapes is None:
            parent_shapes = set()
            
        # 获取当前形状ID
        shape_id = None
        try:
            if hasattr(shape, 'shape_id'):
                shape_id = shape.shape_id
            elif hasattr(shape, 'id'):
                shape_id = shape.id
            else:
                shape_id = id(shape)
                
            # 如果当前形状ID已经在父级形状集合中，说明存在循环引用
            if shape_id in parent_shapes:
                logging.warning(f"检测到循环引用! 形状ID: {shape_id}")
                return True
                
            # 只有处理组合形状时才需要进一步检测
            if isinstance(shape, GroupShape) and hasattr(shape, 'shapes'):
                # 将当前形状ID添加到父级集合中
                new_parents = parent_shapes.copy()
                new_parents.add(shape_id)
                
                # 检测子形状是否有循环引用
                for sub_shape in shape.shapes:
                    if self._detect_circular_reference(sub_shape, new_parents):
                        return True
            
        except Exception as e:
            logging.debug(f"检测循环引用过程中出错: {str(e)}")
            
        return False 
    
    def _collect_all_shapes_with_text(self, slide, shapes_list, processed_ids=None, recursion_depth=0):
        """
        收集幻灯片中所有包含文本的形状
        
        Args:
            slide: 幻灯片对象
            shapes_list: 形状列表，用于保存结果
            processed_ids: 已处理形状ID集合
            recursion_depth: 递归深度
        """
        if recursion_depth > self.max_recursion_depth:
            logging.warning(f"收集文本形状时达到最大递归深度 {self.max_recursion_depth}，停止继续处理")
            return
            
        if processed_ids is None:
            processed_ids = set()
            
        # 如果传入的是幻灯片，处理幻灯片上的所有形状
        if hasattr(slide, 'shapes'):
            shapes = slide.shapes
        # 如果传入的是GroupShape，处理GroupShape中的所有形状
        elif isinstance(slide, GroupShape) and hasattr(slide, 'shapes'):
            shapes = slide.shapes
        else:
            logging.warning(f"无法处理类型为 {type(slide)} 的对象")
            return
            
        try:  
            # 遍历所有形状
            for shape in shapes:
                # 检查是否已处理
                shape_id = id(shape)
                if shape_id in processed_ids:
                    logging.debug(f"跳过已处理的形状: ID={shape_id}")
                    continue
                    
                # 将形状ID添加到已处理集合
                processed_ids.add(shape_id)
                
                # 处理文本框
                if hasattr(shape, 'has_text_frame') and shape.has_text_frame:
                    shapes_list.append(shape)
                    logging.debug(f"添加文本框形状: ID={shape_id}")
                # 处理表格
                elif hasattr(shape, 'has_table') and shape.has_table:
                    shapes_list.append(shape)  # 将表格也作为一种形状添加
                    logging.debug(f"添加表格形状: ID={shape_id}")
                    
                    # 也可以深入处理表格中的每个单元格
                    try:
                        for row in shape.table.rows:
                            for cell in row.cells:
                                if hasattr(cell, 'text_frame'):
                                    # 添加每个单元格的文本框
                                    cell_id = id(cell.text_frame)
                                    if cell_id not in processed_ids:
                                        processed_ids.add(cell_id)
                                        # 创建一个带有文本框的形状代理
                                        class CellProxy:
                                            def __init__(self, text_frame):
                                                self.text_frame = text_frame
                                                self.has_text_frame = True
                                                
                                        shapes_list.append(CellProxy(cell.text_frame))
                                        logging.debug(f"添加表格单元格的文本框: ID={cell_id}")
                    except Exception as e:
                        logging.warning(f"处理表格单元格时出错: {str(e)}")
                
                # 处理组合形状
                elif isinstance(shape, GroupShape):
                    try:
                        # 检测循环引用
                        if any(id(s) == id(shape) for s in shape.shapes):
                            logging.warning(f"检测到组合形状循环引用: ID={shape_id}")
                            continue
                        
                        # 递归处理组合形状
                        self._collect_all_shapes_with_text(shape, shapes_list, processed_ids, recursion_depth + 1)
                    except Exception as e:
                        logging.warning(f"处理组合形状时出错: {str(e)}")
                
                # 处理其他可能有文本的形状类型
                elif hasattr(shape, 'text') and shape.text.strip():
                    # 有些形状可能有text属性但没有text_frame
                    shapes_list.append(shape)
                    logging.debug(f"添加其他文本形状: ID={shape_id}")
                    
        except Exception as e:
            logging.warning(f"收集文本形状时发生错误: {str(e)}")
    
    def _find_text_in_shapes(self, shapes, text_to_find):
        """在形状列表中查找包含特定文本的形状，并返回实际文本内容"""
        for shape in shapes:
            if not hasattr(shape, 'text_frame'):
                continue
                
            text_frame = shape.text_frame
            for paragraph in text_frame.paragraphs:
                para_text = paragraph.text.strip()
                if para_text == text_to_find:
                    return para_text
                    
        return None
        
    def _update_text_in_shapes(self, shapes, old_text, new_text):
        """在形状列表中查找并更新文本内容"""
        for shape in shapes:
            if not hasattr(shape, 'text_frame'):
                continue
                
            text_frame = shape.text_frame
            for paragraph in text_frame.paragraphs:
                para_text = paragraph.text.strip()
                if para_text == old_text:
                    # 更新段落中的所有运行块，保留格式
                    if len(paragraph.runs) > 0:
                        # 保留第一个运行块的格式，清除其他运行块
                        first_run = paragraph.runs[0]
                        while len(paragraph.runs) > 1:
                            p = paragraph._p
                            p.remove(paragraph.runs[-1]._r)
                        # 设置新文本
                        first_run.text = new_text
                    else:
                        # 没有运行块，直接设置文本
                        paragraph.text = new_text
                    return True
                    
        return False
    
    def _fuzzy_update_text(self, shapes, old_text, new_text, find_similar=False):
        """模糊查找并更新文本内容
        
        Args:
            shapes: 形状列表
            old_text: 要查找的文本
            new_text: 要替换的新文本
            find_similar: 是否查找相似文本而非精确匹配
            
        Returns:
            bool: 是否成功更新文本
        """
        logging.info(f"执行模糊文本更新: 查找 '{old_text}', 替换为 '{new_text}'")
        logging.info(f"查找文本长度: {len(old_text)}, 替换文本长度: {len(new_text)}")
        
        # 标准化文本处理 - 保留标点但规范化空白
        def normalize_text(text):
            if not text:
                return ""
            # 只规范化空白字符
            return re.sub(r'\s+', ' ', text).strip()
        
        # 简化查找的文本 - 去除空格、换行等
        def simplify_text(text):
            if not text:
                return ""
            # 移除多余空格和换行符
            text = re.sub(r'\s+', ' ', text).strip()
            # 移除常见的标点符号
            text = re.sub(r'[,.;:!?，。；：！？]', '', text)
            # 转换为小写
            text = text.lower()
            return text
        
        # 预处理查找文本
        normalized_old_text = normalize_text(old_text)
        simplified_old_text = simplify_text(old_text)
        
        logging.debug(f"标准化后的查找文本: '{normalized_old_text}'")
        logging.debug(f"简化后的查找文本: '{simplified_old_text}'")
        
        # 跟踪所有找到的匹配
        all_matches = []
        
        # 遍历所有形状
        for shape_idx, shape in enumerate(shapes):
            if not hasattr(shape, 'text_frame'):
                continue
                
            text_frame = shape.text_frame
            if not text_frame.text.strip():
                continue
            
            # 获取文本框完整内容
            full_text = text_frame.text
            normalized_full = normalize_text(full_text)
            simplified_full = simplify_text(full_text)
            
            # 1. 检查完整文本框的匹配情况
            similarity = difflib.SequenceMatcher(None, normalized_old_text, normalized_full).ratio()
            contains_target = normalized_old_text in normalized_full
            
            if similarity > 0.6 or contains_target:
                logging.info(f"找到可能的文本框匹配，相似度: {similarity:.2f}, 包含匹配: {contains_target}")
                logging.debug(f"文本框内容: '{full_text[:150]}{'...' if len(full_text)>150 else ''}'")
                
                all_matches.append({
                    'shape': shape,
                    'shape_idx': shape_idx,
                    'text_frame': text_frame,
                    'text': full_text,
                    'similarity': similarity,
                    'contains': contains_target,
                    'level': 'shape',
                    'score': similarity * 0.6 + (1.0 if contains_target else 0) * 0.4
                })
                
                # 如果是精确匹配，直接替换整个文本框
                if normalized_old_text == normalized_full:
                    logging.info(f"找到精确文本框匹配，直接替换")
                    # 替换所有段落，而不仅仅是第一段
                    for i, para in enumerate(text_frame.paragraphs):
                        if i == 0:  # 第一段保留格式，设置新文本
                            if len(para.runs) > 0:
                                first_run = para.runs[0]
                                while len(para.runs) > 1:
                                    p = para._p
                                    p.remove(para.runs[-1]._r)
                                first_run.text = new_text
                            else:
                                para.text = new_text
                        else:  # 删除其他段落
                            para.text = ""
                    # 确保只剩下一个段落
                    while len(text_frame.paragraphs) > 1:
                        # 移除多余的段落（如果可能）
                        try:
                            p = text_frame._txBody
                            if len(p.find('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})) > 1:
                                p.remove(p.findall('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})[1])
                        except Exception as e:
                            logging.warning(f"无法删除多余段落: {str(e)}")
                            break
                    return True
            
            # 2. 深入检查每个段落
            for para_idx, para in enumerate(text_frame.paragraphs):
                para_text = para.text
                normalized_para = normalize_text(para_text)
                simplified_para = simplify_text(para_text)
                
                # 如果段落为空，跳过
                if not para_text.strip():
                    continue
                    
                # 计算段落级别的相似度
                para_similarity = difflib.SequenceMatcher(None, normalized_old_text, normalized_para).ratio()
                para_contains = normalized_old_text in normalized_para
                
                if para_similarity > 0.6 or para_contains:
                    logging.info(f"找到段落匹配，相似度: {para_similarity:.2f}, 包含匹配: {para_contains}")
                    logging.debug(f"段落内容: '{para_text}'")
                    
                    all_matches.append({
                        'shape': shape,
                        'shape_idx': shape_idx,
                        'text_frame': text_frame,
                        'paragraph': para,
                        'para_idx': para_idx,
                        'text': para_text,
                        'similarity': para_similarity,
                        'contains': para_contains,
                        'level': 'paragraph',
                        'score': para_similarity * 0.6 + (1.0 if para_contains else 0) * 0.4
                    })
                    
                    # 如果是精确段落匹配，直接替换
                    if normalized_old_text == normalized_para:
                        logging.info(f"找到精确段落匹配，直接替换")
                        if len(para.runs) > 0:
                            first_run = para.runs[0]
                            while len(para.runs) > 1:
                                p = para._p
                                p.remove(para.runs[-1]._r)
                            first_run.text = new_text
                        else:
                            para.text = new_text
                        return True
                
                # 3. 检查每个运行块
                for run_idx, run in enumerate(para.runs):
                    run_text = run.text
                    
                    # 如果运行块很短或为空，跳过
                    if len(run_text) < 10:
                        continue
                        
                    normalized_run = normalize_text(run_text)
                    simplified_run = simplify_text(run_text)
                    
                    # 计算运行块级别的相似度
                    run_similarity = difflib.SequenceMatcher(None, normalized_old_text, normalized_run).ratio()
                    run_contains = normalized_old_text in normalized_run
                    
                    if run_similarity > 0.5 or run_contains:
                        logging.info(f"找到运行块匹配，相似度: {run_similarity:.2f}, 包含匹配: {run_contains}")
                        logging.debug(f"运行块内容: '{run_text}'")
                        
                        all_matches.append({
                            'shape': shape,
                            'shape_idx': shape_idx,
                            'text_frame': text_frame,
                            'paragraph': para,
                            'para_idx': para_idx,
                            'run': run,
                            'run_idx': run_idx,
                            'text': run_text,
                            'similarity': run_similarity,
                            'contains': run_contains,
                            'level': 'run',
                            'score': run_similarity * 0.6 + (1.0 if run_contains else 0) * 0.4
                        })
                        
                        # 如果是精确运行块匹配，直接替换
                        if normalized_old_text == normalized_run:
                            logging.info(f"找到精确运行块匹配，直接替换")
                            run.text = new_text
                            return True
        
        # 如果找到匹配，按评分排序并选择最佳匹配进行替换
        if all_matches:
            # 对匹配结果按评分排序
            all_matches.sort(key=lambda x: x['score'], reverse=True)
            best_match = all_matches[0]
            
            logging.info(f"使用最佳匹配进行替换，级别: {best_match['level']}, 评分: {best_match['score']:.2f}")
            
            # 根据匹配级别进行替换
            if best_match['level'] == 'shape':
                # 替换文本框第一段
                text_frame = best_match['text_frame']
                # 替换所有段落，而不仅仅是第一段
                for i, para in enumerate(text_frame.paragraphs):
                    if i == 0:  # 第一段保留格式，设置新文本
                        if len(para.runs) > 0:
                            first_run = para.runs[0]
                            while len(para.runs) > 1:
                                p = para._p
                                p.remove(para.runs[-1]._r)
                            first_run.text = new_text
                        else:
                            para.text = new_text
                    else:  # 删除其他段落
                        para.text = ""
                # 确保只剩下一个段落
                while len(text_frame.paragraphs) > 1:
                    # 移除多余的段落（如果可能）
                    try:
                        p = text_frame._txBody
                        if len(p.find('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})) > 1:
                            p.remove(p.findall('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})[1])
                    except Exception as e:
                        logging.warning(f"无法删除多余段落: {str(e)}")
                        break
                logging.info(f"成功替换文本框内容")
                return True
                    
            elif best_match['level'] == 'paragraph':
                # 替换段落
                para = best_match['paragraph']
                if len(para.runs) > 0:
                    first_run = para.runs[0]
                    while len(para.runs) > 1:
                        p = para._p
                        p.remove(para.runs[-1]._r)
                    first_run.text = new_text
                else:
                    para.text = new_text
                logging.info(f"成功替换段落")
                return True
                
            elif best_match['level'] == 'run':
                # 替换运行块
                run = best_match['run']
                run.text = new_text
                logging.info(f"成功替换运行块")
                return True
        
        # 没有找到合适的匹配
        logging.warning(f"无法找到匹配文本")
        return False
    
    def _update_text_by_position(self, slide, position, new_text):
        """根据位置信息更新幻灯片中的文本
        
        Args:
            slide: 幻灯片对象
            position: 位置信息字典
            new_text: 要替换的新文本
            
        Returns:
            bool: 是否成功更新文本
        """
        if not position:
            return False
            
        logging.info(f"尝试根据位置信息更新文本: {position}")
        
        # 提取位置信息
        relative_x = position.get('relative_x', None)
        relative_y = position.get('relative_y', None)
        relative_width = position.get('relative_width', None)
        relative_height = position.get('relative_height', None)
        
        # 如果没有相对位置信息，尝试使用绝对位置
        if not all([relative_x, relative_y, relative_width, relative_height]):
            x = position.get('x', None)
            y = position.get('y', None)
            width = position.get('width', None)
            height = position.get('height', None)
            
            # 如果绝对位置也没有，无法继续
            if not all([x, y, width, height]):
                logging.warning("位置信息不完整，无法根据位置匹配文本")
                return False
            
            # 获取幻灯片尺寸
            slide_width = position.get('slide_width', 9144000)  # PowerPoint默认宽度
            slide_height = position.get('slide_height', 6858000)  # PowerPoint默认高度
            
            # 计算相对位置
            relative_x = x / slide_width
            relative_y = y / slide_height
            relative_width = width / slide_width
            relative_height = height / slide_height
        
        # 定义匹配阈值
        POSITION_MATCH_THRESHOLD = 0.2  # 位置偏差容忍度
        
        # 遍历幻灯片中的所有形状
        shapes_with_text = []
        self._collect_all_shapes_with_text(slide, shapes_with_text)
        
        for shape in shapes_with_text:
            # 获取形状位置信息
            shape_position = self._get_text_position(shape)
            if not shape_position:
                continue
                
            # 提取形状位置信息
            shape_relative_x = shape_position.get('relative_x', 0)
            shape_relative_y = shape_position.get('relative_y', 0)
            shape_relative_width = shape_position.get('relative_width', 0)
            shape_relative_height = shape_position.get('relative_height', 0)
            
            # 计算位置差异
            x_diff = abs(relative_x - shape_relative_x)
            y_diff = abs(relative_y - shape_relative_y)
            width_diff = abs(relative_width - shape_relative_width)
            height_diff = abs(relative_height - shape_relative_height)
            
            # 如果位置差异在阈值内，认为是同一个形状
            if (x_diff <= POSITION_MATCH_THRESHOLD and
                y_diff <= POSITION_MATCH_THRESHOLD and
                width_diff <= POSITION_MATCH_THRESHOLD and
                height_diff <= POSITION_MATCH_THRESHOLD):
                
                logging.info(f"找到位置匹配的形状: 位置差异(x={x_diff:.2f}, y={y_diff:.2f}, "
                            f"w={width_diff:.2f}, h={height_diff:.2f})")
                
                # 更新文本内容
                if hasattr(shape, 'text_frame'):
                    text_frame = shape.text_frame
                    # 替换所有段落，而不仅仅是第一段
                    for i, para in enumerate(text_frame.paragraphs):
                        if i == 0:  # 第一段保留格式，设置新文本
                            if len(para.runs) > 0:
                                # 保留第一个run的格式，删除其他run
                                first_run = para.runs[0]
                                while len(para.runs) > 1:
                                    p = para._p
                                    p.remove(para.runs[-1]._r)
                                first_run.text = new_text
                            else:
                                para.text = new_text
                        else:  # 删除其他段落
                            para.text = ""
                    # 确保只剩下一个段落
                    while len(text_frame.paragraphs) > 1:
                        # 移除多余的段落（如果可能）
                        try:
                            p = text_frame._txBody
                            if len(p.find('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})) > 1:
                                p.remove(p.findall('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})[1])
                        except Exception as e:
                            logging.warning(f"无法删除多余段落: {str(e)}")
                            break
                    logging.info(f"成功通过位置更新文本")
                    return True
        
        logging.warning(f"未找到位置匹配的形状")
        return False

    def _find_and_update_text_direct(self, shapes, old_text, new_text):
        """直接查找并更新文本，使用严格匹配策略，最小化文本处理
        
        该方法完全基于 find_specific_text.py 的逻辑，直接在三个级别(文本框、段落、运行块)查找文本
        
        Args:
            shapes: 形状列表
            old_text: 要查找的文本
            new_text: 要替换的新文本
            
        Returns:
            bool: 是否成功更新文本
        """
        logging.info(f"执行直接文本查找与更新: 查找 '{old_text[:50]}{'...' if len(old_text)>50 else ''}'")
        logging.info(f"查找文本长度: {len(old_text)}, 替换文本长度: {len(new_text)}")
        if len(old_text) > 100:
            logging.info(f"查找文本开头100字符: '{old_text[:100]}'")
            logging.info(f"查找文本结尾100字符: '{old_text[-100:]}'")
        
        # 准备查找文本，确保不会增加额外空格
        target_text = old_text.strip()
        
        # 跟踪所有找到的匹配
        all_matches = []
        
        # 遍历每个形状
        for shape_idx, shape in enumerate(shapes):
            if not hasattr(shape, 'text_frame'):
                continue
                
            text_frame = shape.text_frame
            if not text_frame.text.strip():
                continue
            
            # 1. 文本框级别匹配
            full_text = text_frame.text
            
            # 直接检查是否为完全相同的文本
            exact_match = (target_text == full_text.strip())
            
            # 检查是否包含目标文本
            contains_target = target_text in full_text
            
            # 计算相似度
            similarity = difflib.SequenceMatcher(None, target_text, full_text.strip()).ratio()
            
            if exact_match or contains_target or similarity > 0.8:
                logging.info(f"在文本框中找到可能的匹配，完全匹配: {exact_match}, 包含匹配: {contains_target}, 相似度: {similarity:.2f}")
                logging.info(f"文本框内容: '{full_text[:150]}{'...' if len(full_text)>150 else ''}'")
                
                all_matches.append({
                    'shape': shape,
                    'text': full_text,
                    'level': 'shape',
                    'similarity': similarity,
                    'exact': exact_match,
                    'contains': contains_target,
                    'score': (1.0 if exact_match else 0) + (0.8 if contains_target else 0) + similarity * 0.5
                })
                
                # 如果是完全匹配，直接替换整个文本框内容
                if exact_match:
                    logging.info(f"找到完全匹配的文本框，直接替换内容")
                    
                    # 替换所有段落，而不仅仅是第一段
                    for i, para in enumerate(text_frame.paragraphs):
                        if i == 0:  # 第一段保留格式，设置新文本
                            if len(para.runs) > 0:
                                first_run = para.runs[0]
                                while len(para.runs) > 1:
                                    p = para._p
                                    p.remove(para.runs[-1]._r)
                                first_run.text = new_text
                            else:
                                para.text = new_text
                        else:  # 删除其他段落
                            para.text = ""
                    # 确保只剩下一个段落
                    while len(text_frame.paragraphs) > 1:
                        # 移除多余的段落（如果可能）
                        try:
                            p = text_frame._txBody
                            if len(p.find('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})) > 1:
                                p.remove(p.findall('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})[1])
                        except Exception as e:
                            logging.warning(f"无法删除多余段落: {str(e)}")
                            break
                    return True
            
            # 2. 段落级别匹配
            for para_idx, para in enumerate(text_frame.paragraphs):
                para_text = para.text
                
                # 跳过空段落
                if not para_text.strip():
                    continue
                
                # 直接检查是否为完全相同的文本
                para_exact = (target_text == para_text.strip())
                
                # 检查是否包含目标文本
                para_contains = target_text in para_text
                
                # 计算相似度
                para_similarity = difflib.SequenceMatcher(None, target_text, para_text.strip()).ratio()
                
                if para_exact or para_contains or para_similarity > 0.8:
                    logging.info(f"在段落中找到可能的匹配，完全匹配: {para_exact}, 包含匹配: {para_contains}, 相似度: {para_similarity:.2f}")
                    logging.info(f"段落内容: '{para_text}'")
                    
                    all_matches.append({
                        'shape': shape,
                        'paragraph': para,
                        'para_idx': para_idx,
                        'text': para_text,
                        'level': 'paragraph',
                        'similarity': para_similarity,
                        'exact': para_exact,
                        'contains': para_contains,
                        'score': (1.0 if para_exact else 0) + (0.8 if para_contains else 0) + para_similarity * 0.5
                    })
                    
                    # 如果是完全匹配，直接替换整个段落内容
                    if para_exact:
                        logging.info(f"找到完全匹配的段落，直接替换内容")
                        
                        if len(para.runs) > 0:
                            first_run = para.runs[0]
                            while len(para.runs) > 1:
                                p = para._p
                                p.remove(para.runs[-1]._r)
                            first_run.text = new_text
                        else:
                            para.text = new_text
                        return True
                
                # 3. 运行块级别匹配
                for run_idx, run in enumerate(para.runs):
                    run_text = run.text
                    
                    # 跳过空运行块
                    if not run_text.strip():
                        continue
                    
                    # 直接检查是否为完全相同的文本
                    run_exact = (target_text == run_text.strip())
                    
                    # 检查是否包含目标文本
                    run_contains = target_text in run_text
                    
                    # 计算相似度
                    run_similarity = difflib.SequenceMatcher(None, target_text, run_text.strip()).ratio()
                    
                    if run_exact or run_contains or run_similarity > 0.8:
                        logging.info(f"在运行块中找到可能的匹配，完全匹配: {run_exact}, 包含匹配: {run_contains}, 相似度: {run_similarity:.2f}")
                        logging.info(f"运行块内容: '{run_text}'")
                        
                        all_matches.append({
                            'shape': shape,
                            'paragraph': para,
                            'para_idx': para_idx,
                            'run': run,
                            'run_idx': run_idx,
                            'text': run_text,
                            'level': 'run',
                            'similarity': run_similarity,
                            'exact': run_exact,
                            'contains': run_contains,
                            'score': (1.0 if run_exact else 0) + (0.8 if run_contains else 0) + run_similarity * 0.5
                        })
                        
                        # 如果是完全匹配，直接替换整个运行块内容
                        if run_exact:
                            logging.info(f"找到完全匹配的运行块，直接替换内容")
                            run.text = new_text
                            return True
        
        # 如果没有找到完全匹配但有高得分匹配，选择最佳匹配进行替换
        if all_matches:
            # 按得分排序
            all_matches.sort(key=lambda x: x['score'], reverse=True)
            best_match = all_matches[0]
            
            logging.info(f"使用最佳匹配进行替换，级别: {best_match['level']}, 得分: {best_match['score']:.2f}")
            
            # 根据匹配级别进行替换
            if best_match['level'] == 'shape':
                # 替换文本框的第一段
                text_frame = best_match['shape'].text_frame
                # 替换所有段落，而不仅仅是第一段
                for i, para in enumerate(text_frame.paragraphs):
                    if i == 0:  # 第一段保留格式，设置新文本
                        if len(para.runs) > 0:
                            first_run = para.runs[0]
                            while len(para.runs) > 1:
                                p = para._p
                                p.remove(para.runs[-1]._r)
                            first_run.text = new_text
                        else:
                            para.text = new_text
                    else:  # 删除其他段落
                        para.text = ""
                # 确保只剩下一个段落
                while len(text_frame.paragraphs) > 1:
                    # 移除多余的段落（如果可能）
                    try:
                        p = text_frame._txBody
                        if len(p.find('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})) > 1:
                            p.remove(p.findall('.//a:p', namespaces={"a": "http://schemas.openxmlformats.org/drawingml/2006/main"})[1])
                    except Exception as e:
                        logging.warning(f"无法删除多余段落: {str(e)}")
                        break
                logging.info(f"成功替换文本框内容")
                return True
                    
            elif best_match['level'] == 'paragraph':
                # 替换整个段落
                para = best_match['paragraph']
                if len(para.runs) > 0:
                    first_run = para.runs[0]
                    while len(para.runs) > 1:
                        p = para._p
                        p.remove(para.runs[-1]._r)
                    first_run.text = new_text
                else:
                    para.text = new_text
                logging.info(f"成功替换段落内容")
                return True
                
            elif best_match['level'] == 'run':
                # 替换运行块
                run = best_match['run']
                run.text = new_text
                logging.info(f"成功替换运行块内容")
                return True
        
        # 没有找到任何匹配
        logging.warning(f"未找到匹配文本")
        return False

    def _copy_slide_content(self, source_slide, target_slide):
        """
        完全复制源幻灯片的内容到目标幻灯片
        
        Args:
            source_slide: 源幻灯片（原始幻灯片）
            target_slide: 目标幻灯片（结果幻灯片）
        """
        # 清除目标幻灯片中的所有形状
        shapes_to_remove = []
        for shape in target_slide.shapes:
            shapes_to_remove.append(shape)
        
        for shape in shapes_to_remove:
            try:
                sp = shape._element
                sp.getparent().remove(sp)
            except Exception as e:
                logging.warning(f"移除形状失败: {str(e)}")
        
        # 复制源幻灯片的背景
        try:
            # 直接复制整个背景元素
            if hasattr(source_slide, '_element') and hasattr(target_slide, '_element'):
                # 获取源幻灯片的背景元素
                source_bg = source_slide._element.find('.//p:bg', 
                                                     {'p': 'http://schemas.openxmlformats.org/presentationml/2006/main'})
                if source_bg is not None:
                    # 获取目标幻灯片的背景元素
                    target_bg = target_slide._element.find('.//p:bg', 
                                                         {'p': 'http://schemas.openxmlformats.org/presentationml/2006/main'})
                    
                    # 如果目标幻灯片有背景元素，则替换它
                    if target_bg is not None:
                        new_bg = deepcopy(source_bg)
                        parent = target_bg.getparent()
                        parent.replace(target_bg, new_bg)
                    # 如果目标幻灯片没有背景元素，则添加一个
                    else:
                        new_bg = deepcopy(source_bg)
                        # 找到合适的插入位置
                        cSld = target_slide._element.find('.//p:cSld', 
                                                        {'p': 'http://schemas.openxmlformats.org/presentationml/2006/main'})
                        if cSld is not None:
                            cSld.insert(0, new_bg)
        except Exception as e:
            logging.warning(f"复制背景失败: {str(e)}")
            
            # 回退方案：尝试复制背景填充
            try:
                if hasattr(source_slide, 'background') and hasattr(target_slide, 'background'):
                    # 复制背景填充
                    if hasattr(source_slide.background, 'fill') and hasattr(target_slide.background, 'fill'):
                        # 尝试复制背景填充类型
                        if source_slide.background.fill.type != target_slide.background.fill.type:
                            # 这里只能设置一些基本属性，无法完全复制所有背景类型
                            if source_slide.background.fill.type == MSO_FILL.SOLID:
                                if hasattr(source_slide.background.fill.fore_color, 'rgb'):
                                    target_slide.background.fill.solid()
                                    target_slide.background.fill.fore_color.rgb = source_slide.background.fill.fore_color.rgb
            except Exception as e:
                logging.warning(f"复制背景填充失败: {str(e)}")
        
        # 复制源幻灯片的所有形状
        for source_shape in source_slide.shapes:
            try:
                # 获取形状的XML表示
                source_element = source_shape._element
                
                # 创建副本
                new_element = deepcopy(source_element)
                
                # 将副本添加到目标幻灯片
                target_slide._element.spTree.append(new_element)
            except Exception as e:
                logging.warning(f"复制形状失败: {str(e)}")
        
        # 复制幻灯片属性
        try:
            # 复制幻灯片尺寸
            if hasattr(source_slide, 'slide_width') and hasattr(target_slide, 'slide_width'):
                target_slide.slide_width = source_slide.slide_width
            if hasattr(source_slide, 'slide_height') and hasattr(target_slide, 'slide_height'):
                target_slide.slide_height = source_slide.slide_height
        except Exception as e:
            logging.warning(f"复制幻灯片属性失败: {str(e)}")