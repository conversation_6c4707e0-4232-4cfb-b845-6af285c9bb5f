# PPT文本润色工具 (PPT Text Polishing Tool)

A Windows desktop application that uses AI technology to polish and optimize text content in PowerPoint files. The tool maintains the original meaning while improving text fluency and naturalness.

## Key Features

- **批量处理**: 支持同时处理多个PPT文件，提高工作效率
- **智能文本识别**: 自动识别并跳过英文文本和古诗文，专注于中文内容润色
- **格式保留**: 完整保留文本的颜色、字体等格式信息，包括多颜色混合文本
- **质量保证**: 内置智能质量检查机制，确保润色后文本与原文意思一致
- **缓存优化**: 自动缓存处理结果，避免重复润色相同内容
- **并发处理**: 支持异步和并发处理，显著提升处理速度
- **预览功能**: 提供三种预览方式（拖放预览、按钮预览、随机预览）
- **进度监控**: 实时显示处理进度和详细日志信息
- **多模型支持**: 支持多种AI模型提供商（零一万物、OpenRouter等）
- **API管理**: 智能API密钥轮换和限流管理

## Target Users

- **商务人士**: 需要制作高质量PPT演示文稿的企业员工和管理者
- **教育工作者**: 教师、培训师等需要制作教学课件的专业人员
- **咨询顾问**: 需要频繁制作客户汇报PPT的咨询从业者
- **市场营销**: 需要制作产品宣传和营销材料的市场人员
- **学生群体**: 需要制作学术报告和课程作业PPT的学生
- **内容创作者**: 自媒体、培训机构等需要制作内容展示PPT的创作者

## Core Workflow

1. **环境准备**
   - 配置API密钥文件（支持多个密钥轮换使用）
   - 选择AI模型提供商和具体模型
   - 设置自定义润色提示词和最小文本长度

2. **文件选择**
   - 单文件选择：浏览选择单个PPT文件
   - 批量处理：选择包含多个PPT文件的文件夹
   - 自动加载：从默认下载文件夹自动加载最新PPT文件
   - 拖放操作：直接拖放文件到界面进行预览

3. **处理配置**
   - 指定要处理的幻灯片范围（如"2-10 12 15-20"）
   - 设置输出文件夹路径
   - 选择生成的修改版本数量

4. **智能处理**
   - 自动转换PPT格式为PPTX（如需要）
   - 并发处理多个幻灯片，提高效率
   - 智能识别文本类型，跳过英文和古诗文
   - 保留原始文本格式（颜色、字体等）
   - 使用AI进行文本润色优化

5. **质量控制**
   - 自动进行文本质量检查
   - 验证润色后文本与原文意思一致性
   - 确保序号和标点符号完整保留
   - 缓存处理结果避免重复处理

6. **结果输出**
   - 生成润色后的PPT文件
   - 提供多种预览方式查看修改效果
   - 支持随机预览未审阅文件
   - 详细的处理日志和统计信息
