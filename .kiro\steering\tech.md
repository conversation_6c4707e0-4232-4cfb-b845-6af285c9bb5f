# Technology Stack

## Core Technologies

### Frontend & GUI
- **PyQt5 (>=5.15.4)** - 跨平台GUI框架，提供现代化桌面应用界面
  - 主窗口界面 (`MainWindow`)
  - 模型配置对话框 (`ModelConfigDialog`) 
  - 预览窗口 (`PreviewWindow`)
  - 拖放功能和实时进度显示

### Document Processing
- **python-pptx (>=0.6.19)** - PowerPoint文档处理核心库
  - PPT/PPTX文件读取和写入
  - 幻灯片内容解析和修改
  - 文本格式保留（颜色、字体等）
- **win32com** - Windows COM接口，用于PPT格式转换

### HTTP & API Client
- **httpx (>=0.23.0)** - 现代异步HTTP客户端
  - 支持异步和同步请求
  - 连接池管理和超时控制
  - HTTP/2支持和自动重试机制

### Caching & Performance
- **diskcache (>=5.4.0)** - 高性能磁盘缓存系统
  - API响应结果缓存
  - 避免重复处理相同内容
  - 自动过期和清理机制

### Reliability & Error Handling
- **tenacity (>=8.0.1)** - 重试和容错处理库
  - API调用失败自动重试
  - 指数退避策略
  - 自定义重试条件和停止策略

## AI Integration

### Multi-Provider Support
- **零一万物 (Yi)** - 主要AI服务提供商
  - Yi-Lightning模型，专注中文文本处理
  - 高质量文本润色和优化
- **OpenRouter** - 多模型聚合平台
  - Claude 3 Opus/Sonnet (Anthropic)
  - GPT-4o/GPT-4 Turbo (OpenAI)
  - Gemini Pro/2.5 Pro (Google)
  - Llama 3/4 (Meta)
  - Mistral Large (Mistral AI)
  - DeepSeek V3 (DeepSeek)
  - Qwen2.5 VL (阿里巴巴)

### OpenAI Compatible API
- **openai (>=1.3.0)** - OpenAI Python客户端库
  - 统一的API接口标准
  - 支持多种AI模型提供商
  - 流式响应和批量处理

### Smart API Management
- **密钥轮换机制** - 自动切换多个API密钥，避免限流
- **负载均衡** - 智能分配请求到不同模型
- **使用统计** - 详细的API调用统计和成本分析
- **缓存优化** - 相同内容避免重复调用API

## Architecture Patterns

### Modular Architecture (模块化架构)
```
ppt_modifier/
├── api/           # API管理层 - 外部服务集成
├── gui/           # 表示层 - 用户界面和交互
├── processor/     # 业务层 - 核心处理逻辑  
├── converter/     # 转换层 - 文件格式转换
└── utils/         # 工具层 - 通用工具函数
```

### Layered Architecture (分层架构)
1. **Presentation Layer** - PyQt5 GUI界面层
2. **Business Logic Layer** - 文本处理和润色逻辑
3. **Service Layer** - AI API调用和管理
4. **Data Access Layer** - 文件I/O和缓存管理

### Design Patterns
- **Factory Pattern** - AI模型提供商工厂
- **Strategy Pattern** - 不同文本处理策略
- **Observer Pattern** - GUI进度更新和事件通知
- **Singleton Pattern** - 配置管理和缓存实例
- **Command Pattern** - 异步任务队列管理

### Asynchronous Processing
- **并发处理** - 多线程处理多个PPT文件
- **异步API调用** - 非阻塞的AI服务请求
- **任务队列** - 有序处理大批量文件
- **进度监控** - 实时更新处理状态

## Common Commands

### Development Setup
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境 (Windows)
venv\Scripts\activate

# 安装依赖包
pip install -r requirements.txt

# 安装开发依赖 (可选)
pip install pytest pytest-qt black flake8
```

### Running the Application
```bash
# 标准启动方式
python run.py

# 使用批处理文件启动 (Windows)
run_with_env.bat          # 基础环境启动
run_optimized.bat         # 性能优化启动

# 直接运行主模块
python -m ppt_modifier.main

# 调试模式启动
python -c "import logging; logging.basicConfig(level=logging.DEBUG); from ppt_modifier.main import main; main()"
```

### Testing Components
```bash
# 运行所有测试
python -m pytest

# 运行特定测试文件
python test_preview_functions.py
python test_consistency_verification.py
python test_text_replacement.py

# 运行简化测试
python test_preview_functions_simple.py
python test_simple_consistency.py

# Qt界面测试
python test_qt.py

# 代码修改功能测试
python test_modified_code.py
```

### Utility Scripts
```bash
# PPT文本查找工具
python ppt_text_finder.py

# PPT文本替换工具  
python ppt_text_replacer.py

# 特定文本查找
python find_specific_text.py

# 一致性验证报告
python -c "from test_consistency_verification import generate_report; generate_report()"
```

## Build System

### Environment Configuration
- **虚拟环境管理** - 使用venv隔离Python依赖
- **环境变量设置** - Qt插件路径和性能优化参数
- **编码配置** - UTF-8编码支持中文处理

### Performance Optimization
```batch
# 性能优化环境变量
set PYTHONOPTIMIZE=1        # 启用Python字节码优化
set NUMEXPR_MAX_THREADS=8   # 设置数值计算线程数
set PYTHONTRACEMALLOC=0     # 禁用内存跟踪
set PYTHONASYNCIODEBUG=0    # 禁用异步调试
```

### Packaging & Distribution
- **requirements.txt** - 精确的依赖版本管理
- **模块化打包** - 清晰的包结构便于分发
- **配置文件分离** - JSON配置文件便于部署
- **日志系统** - 完整的运行时日志记录

### Cache Management
- **.api_cache/** - API响应缓存目录
- **.api_stats.json** - API使用统计文件
- **temp/** - 临时文件处理目录
- **自动清理机制** - 定期清理过期缓存

### Development Tools
- **代码格式化** - Black代码格式化工具
- **代码检查** - Flake8静态代码分析
- **测试框架** - pytest和pytest-qt测试套件
- **日志分析** - 结构化日志便于问题诊断