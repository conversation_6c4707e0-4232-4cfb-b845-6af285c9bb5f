# Project Structure

## Root Directory
```
ppt_modifier/                    # 主应用程序包
├── run.py                      # 程序启动入口脚本
├── requirements.txt            # Python依赖包列表
├── README.md                   # 项目说明文档
├── PPT_Tool_Description.md     # 工具详细描述文档
├── model_config.json           # AI模型配置文件
├── ppt_modifier.log           # 应用程序日志文件
├── .api_cache/                # API请求缓存目录
├── .api_stats.json            # API使用统计信息
├── .gitignore                 # Git忽略文件配置
├── temp/                      # 临时文件目录
└── venv/                      # Python虚拟环境目录
```

## Main Package: `ppt_modifier`

### Core Modules
- **`main.py`** - 主程序入口，负责应用程序初始化和GUI启动
- **`__init__.py`** - 包初始化文件，定义版本信息

### Subpackages

#### `api/` - API管理模块
- **`api_manager.py`** - API请求管理器，处理AI模型调用和密钥轮换
- **`model_config.py`** - 模型配置管理，支持多种AI服务提供商

#### `converter/` - 文件转换模块  
- **`ppt_converter.py`** - PPT格式转换器，处理PPT到PPTX的转换

#### `gui/` - 图形用户界面模块
- **`main_window.py`** - 主窗口界面，提供用户交互功能
- **`model_config_dialog.py`** - 模型配置对话框
- **`preview_window.py`** - 预览窗口，支持多种预览方式

#### `processor/` - 文本处理核心模块
- **`ppt_processor.py`** - PPT文件处理器，负责幻灯片内容解析和修改
- **`text_processor.py`** - 文本润色处理器，实现AI文本优化逻辑

#### `utils/` - 工具函数模块
- **`file_utils.py`** - 文件操作工具函数

#### `tools/` - 扩展工具模块
- 当前为空，预留用于未来功能扩展

## Test Files
- **`test_consistency_verification.py`** - 一致性验证测试
- **`test_modified_code.py`** - 代码修改功能测试  
- **`test_preview_functions.py`** - 预览功能完整测试套件
- **`test_preview_functions_simple.py`** - 预览功能简化测试
- **`test_qt.py`** - Qt界面组件测试
- **`test_simple_consistency.py`** - 简单一致性测试
- **`test_text_replacement.py`** - 文本替换功能测试

## Utility Scripts
- **`ppt_text_finder.py`** - PPT文本查找工具，支持多种匹配策略
- **`ppt_text_replacer.py`** - PPT文本替换工具，提供精确和模糊替换
- **`find_specific_text.py`** - 特定文本查找脚本，用于调试和分析
- **`run_optimized.bat`** - Windows优化启动脚本
- **`run_with_env.bat`** - 带环境配置的启动脚本

## Naming Conventions

### 文件命名规范
- **模块文件**: 使用小写字母和下划线 (`snake_case`)
  - 例: `api_manager.py`, `text_processor.py`
- **测试文件**: 以 `test_` 前缀开头
  - 例: `test_preview_functions.py`
- **工具脚本**: 使用描述性名称，以功能为导向
  - 例: `ppt_text_finder.py`, `find_specific_text.py`
- **批处理文件**: 使用 `run_` 前缀，描述启动方式
  - 例: `run_optimized.bat`

### 包和模块组织
- **功能模块**: 按功能领域分组 (`api`, `gui`, `processor`)
- **工具模块**: 通用工具放在 `utils` 包中
- **扩展模块**: 可选功能放在 `tools` 包中

### 类和函数命名
- **类名**: 使用大驼峰命名法 (`PascalCase`)
  - 例: `PPTTextFinder`, `MainWindow`
- **函数名**: 使用小写字母和下划线 (`snake_case`)
  - 例: `find_text()`, `normalize_text()`
- **常量**: 使用全大写字母和下划线
  - 例: `TARGET_TEXT`, `API_TIMEOUT`

## File Organization Principles

### 模块化设计
- **单一职责**: 每个模块专注于特定功能领域
- **低耦合**: 模块间依赖关系清晰，避免循环依赖
- **高内聚**: 相关功能集中在同一模块内

### 分层架构
1. **表示层** (`gui/`) - 用户界面和交互逻辑
2. **业务层** (`processor/`) - 核心业务逻辑和文本处理
3. **服务层** (`api/`) - 外部服务集成和API管理
4. **工具层** (`utils/`, `converter/`) - 通用工具和转换功能

### 配置和数据分离
- **配置文件**: 独立的JSON配置文件 (`model_config.json`)
- **缓存数据**: 专门的缓存目录 (`.api_cache/`)
- **日志文件**: 统一的日志管理 (`ppt_modifier.log`)
- **临时文件**: 独立的临时目录 (`temp/`)

### 测试组织
- **单元测试**: 针对特定功能模块的测试
- **集成测试**: 跨模块功能的综合测试  
- **一致性测试**: 确保功能行为的一致性
- **工具测试**: 独立工具脚本的验证测试